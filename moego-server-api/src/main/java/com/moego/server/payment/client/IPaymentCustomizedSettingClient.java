package com.moego.server.payment.client;

import com.moego.server.payment.api.IPaymentCustomizedSettingService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@FeignClient(
        value = "moego-payment-server",
        url = "${moego.server.url.payment}",
        contextId = "IPaymentCustomizedSettingClient")
public interface IPaymentCustomizedSettingClient extends IPaymentCustomizedSettingService {}
