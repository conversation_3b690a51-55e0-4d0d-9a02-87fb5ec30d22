package com.moego.server.grooming.dto.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/19
 */
@Data
public class OBPetDataDTO {

    @Schema(description = "pet id")
    private Integer petId;

    @Schema(description = "pet weight")
    private String weight;

    @NotNull
    @Schema(description = "pet type id, 1-dog, 2-cat, 11-other")
    private Integer petTypeId;

    @NotEmpty
    @Schema(description = "pet breed")
    private String breed;

    @Schema(description = "coat")
    private String coat;

    @Schema(description = "hair length, compatible with old version")
    private String hairLength;

    @Schema(description = "staff id, can be null")
    private Integer staffId;

    @Schema(description = "pet index")
    private Integer petIndex;

    @Schema(description = "service ids")
    private List<Integer> serviceIds;

    @Schema(description = "service staff map")
    private Map<Integer /* service id */, Set<Integer> /* staff id */> serviceStaffMap;

    // @Schema(description = "service date")
    // private String serviceDate;

    @Schema(description = "service start time")
    private Integer serviceStartTime;
}
