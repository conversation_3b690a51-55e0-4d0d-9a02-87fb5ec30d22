package com.moego.server.grooming.dto;

import com.moego.idl.models.smart_scheduler.v1.TimeSlotType;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OBAvailableTimeSlotDetailDTO {

    private Integer time;

    private Integer capacity;

    private Integer occupiedCount;

    private TimeSlotType timeSlotType;

    private List<TimeSlotType> timeSlotTypes;
}
