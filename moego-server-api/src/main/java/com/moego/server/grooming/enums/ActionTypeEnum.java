package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum ActionTypeEnum {
    UNKNOWN(""),
    CREATE(AppointmentAction.CREATE),
    UPDATE_STATUS(AppointmentAction.UPDATE_STATUS),
    CHANGE_CHECK_IN_TIME(AppointmentAction.CHANGE_CHECK_IN_TIME),
    CHANGE_CHECK_OUT_TIME(AppointmentAction.CHANGE_CHECK_OUT_TIME),
    SEND_NOTIFICATION(AppointmentAction.SEND_NOTIFICATION),
    RESCHEDULE(AppointmentAction.RESCHEDULE),
    CUSTOMER_REPLY(AppointmentAction.CUSTOMER_REPLY),
    CANCEL(AppointmentAction.CANCEL),
    EDIT_PET_AND_SERVICES(AppointmentAction.EDIT_PET_AND_SERVICES),
    UPDATE_NOTIFICATION_RESULT(AppointmentAction.UPDATE_NOTIFICATION_STATUS),
    AUTO_ROLLOVER(AppointmentAction.AUTO_ROLLOVER),
    CLIENT_UPDATE_OB(AppointmentAction.CLIENT_UPDATE_OB),
    CLIENT_UPDATE_APPOINTMENT(AppointmentAction.CLIENT_UPDATE_APPOINTMENT);

    private final String value;

    @JsonValue
    public String value() {
        return value;
    }

    public static ActionTypeEnum fromString(String str) {
        if (str == null) {
            return UNKNOWN;
        }
        for (ActionTypeEnum actionTypeEnum : ActionTypeEnum.values()) {
            if (actionTypeEnum.value.equalsIgnoreCase(str)) {
                return actionTypeEnum;
            }
        }
        return UNKNOWN;
    }
}
