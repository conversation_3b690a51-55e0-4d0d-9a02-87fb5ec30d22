package com.moego.server.grooming.params;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class BookOnlineQuestionParams {

    private Integer id;
    private Integer businessId;
    private Long companyId;
    private String question;

    @Size(max = 255)
    private String placeholder;

    private Byte isShow;
    private Byte isRequired;
    private Byte type;
    private Byte isAllowDelete;
    private Byte isAllowChange;
    private Byte isAllowEdit;
    private Integer sort;
    private Byte status;
    private Long createTime;
    private Long updateTime;
    private Byte questionType;
    private String extraJson;
    /**
     * @deprecated by <PERSON> since 2025/7/9, use {@link #newClientAccessMode} and {@link #existingClientAccessMode} instead
     */
    @Deprecated
    Integer acceptCustomerType;
    /**
     * @deprecated by <PERSON> since 2025/6/24, use {@link #newPetAccessMode} and {@link #existingPetAccessMode} instead
     */
    @Deprecated
    Integer acceptPetEntryType;
    /**
     * @see com.moego.idl.models.online_booking.v1.NewPetAccessMode
     */
    @Nullable
    Integer newPetAccessMode;
    /**
     * @see com.moego.idl.models.online_booking.v1.ExistingPetAccessMode
     */
    @Nullable
    Integer existingPetAccessMode;
    /**
     * @see com.moego.idl.models.online_booking.v1.NewClientAccessMode
     */
    @Nullable
    Integer newClientAccessMode;
    /**
     * @see com.moego.idl.models.online_booking.v1.ExistingClientAccessMode
     */
    @Nullable
    Integer existingClientAccessMode;
}
