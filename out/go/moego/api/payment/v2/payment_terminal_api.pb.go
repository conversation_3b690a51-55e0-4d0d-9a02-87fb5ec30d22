// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/payment/v2/payment_terminal_api.proto

package paymentapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for RetrieveSdkData
type RetrieveSdkDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// Request params
	//
	// Types that are assignable to Params:
	//
	//	*RetrieveSdkDataParams_AdyenParams_
	Params isRetrieveSdkDataParams_Params `protobuf_oneof:"params"`
}

func (x *RetrieveSdkDataParams) Reset() {
	*x = RetrieveSdkDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataParams) ProtoMessage() {}

func (x *RetrieveSdkDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataParams.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{0}
}

func (x *RetrieveSdkDataParams) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (m *RetrieveSdkDataParams) GetParams() isRetrieveSdkDataParams_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *RetrieveSdkDataParams) GetAdyenParams() *RetrieveSdkDataParams_AdyenParams {
	if x, ok := x.GetParams().(*RetrieveSdkDataParams_AdyenParams_); ok {
		return x.AdyenParams
	}
	return nil
}

type isRetrieveSdkDataParams_Params interface {
	isRetrieveSdkDataParams_Params()
}

type RetrieveSdkDataParams_AdyenParams_ struct {
	// Data for Adyen
	AdyenParams *RetrieveSdkDataParams_AdyenParams `protobuf:"bytes,2,opt,name=adyen_params,json=adyenParams,proto3,oneof"`
}

func (*RetrieveSdkDataParams_AdyenParams_) isRetrieveSdkDataParams_Params() {}

// Response for RetrieveSdkData
type RetrieveSdkDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付数据
	//
	// Types that are assignable to Data:
	//
	//	*RetrieveSdkDataResult_AdyenData_
	Data isRetrieveSdkDataResult_Data `protobuf_oneof:"data"`
}

func (x *RetrieveSdkDataResult) Reset() {
	*x = RetrieveSdkDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataResult) ProtoMessage() {}

func (x *RetrieveSdkDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataResult.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{1}
}

func (m *RetrieveSdkDataResult) GetData() isRetrieveSdkDataResult_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *RetrieveSdkDataResult) GetAdyenData() *RetrieveSdkDataResult_AdyenData {
	if x, ok := x.GetData().(*RetrieveSdkDataResult_AdyenData_); ok {
		return x.AdyenData
	}
	return nil
}

type isRetrieveSdkDataResult_Data interface {
	isRetrieveSdkDataResult_Data()
}

type RetrieveSdkDataResult_AdyenData_ struct {
	// adyen data
	AdyenData *RetrieveSdkDataResult_AdyenData `protobuf:"bytes,1,opt,name=adyen_data,json=adyenData,proto3,oneof"`
}

func (*RetrieveSdkDataResult_AdyenData_) isRetrieveSdkDataResult_Data() {}

// Adyen params
type RetrieveSdkDataParams_AdyenParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Setup token
	SetupToken string `protobuf:"bytes,1,opt,name=setup_token,json=setupToken,proto3" json:"setup_token,omitempty"`
}

func (x *RetrieveSdkDataParams_AdyenParams) Reset() {
	*x = RetrieveSdkDataParams_AdyenParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataParams_AdyenParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataParams_AdyenParams) ProtoMessage() {}

func (x *RetrieveSdkDataParams_AdyenParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataParams_AdyenParams.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataParams_AdyenParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RetrieveSdkDataParams_AdyenParams) GetSetupToken() string {
	if x != nil {
		return x.SetupToken
	}
	return ""
}

// adyen data
type RetrieveSdkDataResult_AdyenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SDK data
	SdkData string `protobuf:"bytes,1,opt,name=sdk_data,json=sdkData,proto3" json:"sdk_data,omitempty"`
}

func (x *RetrieveSdkDataResult_AdyenData) Reset() {
	*x = RetrieveSdkDataResult_AdyenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataResult_AdyenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataResult_AdyenData) ProtoMessage() {}

func (x *RetrieveSdkDataResult_AdyenData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataResult_AdyenData.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataResult_AdyenData) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RetrieveSdkDataResult_AdyenData) GetSdkData() string {
	if x != nil {
		return x.SdkData
	}
	return ""
}

var File_moego_api_payment_v2_payment_terminal_api_proto protoreflect.FileDescriptor

var file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf8, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x53, 0x64, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x47, 0x0a,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x0c, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x0a, 0x0b, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x75, 0x70, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x9f,
	0x01, 0x0a, 0x15, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x0a, 0x61, 0x64, 0x79, 0x65,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x26, 0x0a, 0x09, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x64, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x64, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x32, 0x85, 0x01, 0x0a, 0x16, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6b, 0x0a, 0x0f, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x78, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDescOnce sync.Once
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData = file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc
)

func file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP() []byte {
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDescOnce.Do(func() {
		file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData)
	})
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData
}

var file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_api_payment_v2_payment_terminal_api_proto_goTypes = []interface{}{
	(*RetrieveSdkDataParams)(nil),             // 0: moego.api.payment.v2.RetrieveSdkDataParams
	(*RetrieveSdkDataResult)(nil),             // 1: moego.api.payment.v2.RetrieveSdkDataResult
	(*RetrieveSdkDataParams_AdyenParams)(nil), // 2: moego.api.payment.v2.RetrieveSdkDataParams.AdyenParams
	(*RetrieveSdkDataResult_AdyenData)(nil),   // 3: moego.api.payment.v2.RetrieveSdkDataResult.AdyenData
	(v2.ChannelType)(0),                       // 4: moego.models.payment.v2.ChannelType
}
var file_moego_api_payment_v2_payment_terminal_api_proto_depIdxs = []int32{
	4, // 0: moego.api.payment.v2.RetrieveSdkDataParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	2, // 1: moego.api.payment.v2.RetrieveSdkDataParams.adyen_params:type_name -> moego.api.payment.v2.RetrieveSdkDataParams.AdyenParams
	3, // 2: moego.api.payment.v2.RetrieveSdkDataResult.adyen_data:type_name -> moego.api.payment.v2.RetrieveSdkDataResult.AdyenData
	0, // 3: moego.api.payment.v2.PaymentTerminalService.RetrieveSdkData:input_type -> moego.api.payment.v2.RetrieveSdkDataParams
	1, // 4: moego.api.payment.v2.PaymentTerminalService.RetrieveSdkData:output_type -> moego.api.payment.v2.RetrieveSdkDataResult
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_api_payment_v2_payment_terminal_api_proto_init() }
func file_moego_api_payment_v2_payment_terminal_api_proto_init() {
	if File_moego_api_payment_v2_payment_terminal_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataParams_AdyenParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataResult_AdyenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*RetrieveSdkDataParams_AdyenParams_)(nil),
	}
	file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*RetrieveSdkDataResult_AdyenData_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_payment_v2_payment_terminal_api_proto_goTypes,
		DependencyIndexes: file_moego_api_payment_v2_payment_terminal_api_proto_depIdxs,
		MessageInfos:      file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes,
	}.Build()
	File_moego_api_payment_v2_payment_terminal_api_proto = out.File
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc = nil
	file_moego_api_payment_v2_payment_terminal_api_proto_goTypes = nil
	file_moego_api_payment_v2_payment_terminal_api_proto_depIdxs = nil
}
