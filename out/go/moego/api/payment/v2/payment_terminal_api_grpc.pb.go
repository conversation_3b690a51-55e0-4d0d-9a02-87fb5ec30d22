// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/payment/v2/payment_terminal_api.proto

package paymentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PaymentTerminalServiceClient is the client API for PaymentTerminalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentTerminalServiceClient interface {
	// Retrieve required data to initialize the client side terminal SDK.
	RetrieveSdkData(ctx context.Context, in *RetrieveSdkDataParams, opts ...grpc.CallOption) (*RetrieveSdkDataResult, error)
}

type paymentTerminalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentTerminalServiceClient(cc grpc.ClientConnInterface) PaymentTerminalServiceClient {
	return &paymentTerminalServiceClient{cc}
}

func (c *paymentTerminalServiceClient) RetrieveSdkData(ctx context.Context, in *RetrieveSdkDataParams, opts ...grpc.CallOption) (*RetrieveSdkDataResult, error) {
	out := new(RetrieveSdkDataResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentTerminalService/RetrieveSdkData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentTerminalServiceServer is the server API for PaymentTerminalService service.
// All implementations must embed UnimplementedPaymentTerminalServiceServer
// for forward compatibility
type PaymentTerminalServiceServer interface {
	// Retrieve required data to initialize the client side terminal SDK.
	RetrieveSdkData(context.Context, *RetrieveSdkDataParams) (*RetrieveSdkDataResult, error)
	mustEmbedUnimplementedPaymentTerminalServiceServer()
}

// UnimplementedPaymentTerminalServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentTerminalServiceServer struct {
}

func (UnimplementedPaymentTerminalServiceServer) RetrieveSdkData(context.Context, *RetrieveSdkDataParams) (*RetrieveSdkDataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrieveSdkData not implemented")
}
func (UnimplementedPaymentTerminalServiceServer) mustEmbedUnimplementedPaymentTerminalServiceServer() {
}

// UnsafePaymentTerminalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentTerminalServiceServer will
// result in compilation errors.
type UnsafePaymentTerminalServiceServer interface {
	mustEmbedUnimplementedPaymentTerminalServiceServer()
}

func RegisterPaymentTerminalServiceServer(s grpc.ServiceRegistrar, srv PaymentTerminalServiceServer) {
	s.RegisterService(&PaymentTerminalService_ServiceDesc, srv)
}

func _PaymentTerminalService_RetrieveSdkData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveSdkDataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTerminalServiceServer).RetrieveSdkData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentTerminalService/RetrieveSdkData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTerminalServiceServer).RetrieveSdkData(ctx, req.(*RetrieveSdkDataParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentTerminalService_ServiceDesc is the grpc.ServiceDesc for PaymentTerminalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentTerminalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.payment.v2.PaymentTerminalService",
	HandlerType: (*PaymentTerminalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RetrieveSdkData",
			Handler:    _PaymentTerminalService_RetrieveSdkData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/payment/v2/payment_terminal_api.proto",
}
