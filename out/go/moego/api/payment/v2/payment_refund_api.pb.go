// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/payment/v2/payment_refund_api.proto

package paymentapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// refund params
type RefundParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment id
	PaymentId int64 `protobuf:"varint,1,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// 退款金额
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *RefundParams) Reset() {
	*x = RefundParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundParams) ProtoMessage() {}

func (x *RefundParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundParams.ProtoReflect.Descriptor instead.
func (*RefundParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP(), []int{0}
}

func (x *RefundParams) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *RefundParams) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

// refund result
type RefundResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// refund model
	RefundView *v2.RefundView `protobuf:"bytes,1,opt,name=refund_view,json=refundView,proto3" json:"refund_view,omitempty"`
}

func (x *RefundResult) Reset() {
	*x = RefundResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundResult) ProtoMessage() {}

func (x *RefundResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundResult.ProtoReflect.Descriptor instead.
func (*RefundResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP(), []int{1}
}

func (x *RefundResult) GetRefundView() *v2.RefundView {
	if x != nil {
		return x.RefundView
	}
	return nil
}

// get refund params
type GetRefundParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// refund id
	RefundId int64 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
}

func (x *GetRefundParams) Reset() {
	*x = GetRefundParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundParams) ProtoMessage() {}

func (x *GetRefundParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundParams.ProtoReflect.Descriptor instead.
func (*GetRefundParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetRefundParams) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

// get refund result
type GetRefundResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// refund view
	RefundView *v2.RefundView `protobuf:"bytes,1,opt,name=refund_view,json=refundView,proto3" json:"refund_view,omitempty"`
}

func (x *GetRefundResult) Reset() {
	*x = GetRefundResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundResult) ProtoMessage() {}

func (x *GetRefundResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundResult.ProtoReflect.Descriptor instead.
func (*GetRefundResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetRefundResult) GetRefundView() *v2.RefundView {
	if x != nil {
		return x.RefundView
	}
	return nil
}

// list refund params
type ListRefundParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页查询请求
	PaginationRequest *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// filter
	Filter *ListRefundParams_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListRefundParams) Reset() {
	*x = ListRefundParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRefundParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRefundParams) ProtoMessage() {}

func (x *ListRefundParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRefundParams.ProtoReflect.Descriptor instead.
func (*ListRefundParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListRefundParams) GetPaginationRequest() *v21.PaginationRequest {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListRefundParams) GetFilter() *ListRefundParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list refund result
type ListRefundResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页
	PaginationRequest *v21.PaginationResponse `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// 退款列表
	RefundViews []*v2.RefundView `protobuf:"bytes,2,rep,name=refund_views,json=refundViews,proto3" json:"refund_views,omitempty"`
}

func (x *ListRefundResult) Reset() {
	*x = ListRefundResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRefundResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRefundResult) ProtoMessage() {}

func (x *ListRefundResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRefundResult.ProtoReflect.Descriptor instead.
func (*ListRefundResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListRefundResult) GetPaginationRequest() *v21.PaginationResponse {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListRefundResult) GetRefundViews() []*v2.RefundView {
	if x != nil {
		return x.RefundViews
	}
	return nil
}

// filter
type ListRefundParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// order id
	OrderIds []int64 `protobuf:"varint,2,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// order payment id
	OrderPaymentIds []int64 `protobuf:"varint,3,rep,packed,name=order_payment_ids,json=orderPaymentIds,proto3" json:"order_payment_ids,omitempty"`
	// payment id
	PaymentIds []int64 `protobuf:"varint,4,rep,packed,name=payment_ids,json=paymentIds,proto3" json:"payment_ids,omitempty"`
	// refund id list
	RefundIds []int64 `protobuf:"varint,5,rep,packed,name=refund_ids,json=refundIds,proto3" json:"refund_ids,omitempty"`
	// 查询时间范围
	TimePeriod *v1.TimePeriod `protobuf:"bytes,6,opt,name=time_period,json=timePeriod,proto3" json:"time_period,omitempty"`
}

func (x *ListRefundParams_Filter) Reset() {
	*x = ListRefundParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRefundParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRefundParams_Filter) ProtoMessage() {}

func (x *ListRefundParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRefundParams_Filter.ProtoReflect.Descriptor instead.
func (*ListRefundParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListRefundParams_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListRefundParams_Filter) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *ListRefundParams_Filter) GetOrderPaymentIds() []int64 {
	if x != nil {
		return x.OrderPaymentIds
	}
	return nil
}

func (x *ListRefundParams_Filter) GetPaymentIds() []int64 {
	if x != nil {
		return x.PaymentIds
	}
	return nil
}

func (x *ListRefundParams_Filter) GetRefundIds() []int64 {
	if x != nil {
		return x.RefundIds
	}
	return nil
}

func (x *ListRefundParams_Filter) GetTimePeriod() *v1.TimePeriod {
	if x != nil {
		return x.TimePeriod
	}
	return nil
}

var File_moego_api_payment_v2_payment_refund_api_proto protoreflect.FileDescriptor

var file_moego_api_payment_v2_payment_refund_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x59, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x44, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x76, 0x69,
	0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x22, 0x2e, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x44, 0x0a, 0x0b,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x56, 0x69,
	0x65, 0x77, 0x22, 0x9f, 0x03, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x1a, 0xf1, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x12, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x56,
	0x69, 0x65, 0x77, 0x73, 0x32, 0x9a, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x50, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x12, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x12, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x78, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_api_payment_v2_payment_refund_api_proto_rawDescOnce sync.Once
	file_moego_api_payment_v2_payment_refund_api_proto_rawDescData = file_moego_api_payment_v2_payment_refund_api_proto_rawDesc
)

func file_moego_api_payment_v2_payment_refund_api_proto_rawDescGZIP() []byte {
	file_moego_api_payment_v2_payment_refund_api_proto_rawDescOnce.Do(func() {
		file_moego_api_payment_v2_payment_refund_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_payment_v2_payment_refund_api_proto_rawDescData)
	})
	return file_moego_api_payment_v2_payment_refund_api_proto_rawDescData
}

var file_moego_api_payment_v2_payment_refund_api_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_api_payment_v2_payment_refund_api_proto_goTypes = []interface{}{
	(*RefundParams)(nil),            // 0: moego.api.payment.v2.RefundParams
	(*RefundResult)(nil),            // 1: moego.api.payment.v2.RefundResult
	(*GetRefundParams)(nil),         // 2: moego.api.payment.v2.GetRefundParams
	(*GetRefundResult)(nil),         // 3: moego.api.payment.v2.GetRefundResult
	(*ListRefundParams)(nil),        // 4: moego.api.payment.v2.ListRefundParams
	(*ListRefundResult)(nil),        // 5: moego.api.payment.v2.ListRefundResult
	(*ListRefundParams_Filter)(nil), // 6: moego.api.payment.v2.ListRefundParams.Filter
	(*money.Money)(nil),             // 7: google.type.Money
	(*v2.RefundView)(nil),           // 8: moego.models.payment.v2.RefundView
	(*v21.PaginationRequest)(nil),   // 9: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),  // 10: moego.utils.v2.PaginationResponse
	(*v1.TimePeriod)(nil),           // 11: moego.utils.v1.TimePeriod
}
var file_moego_api_payment_v2_payment_refund_api_proto_depIdxs = []int32{
	7,  // 0: moego.api.payment.v2.RefundParams.amount:type_name -> google.type.Money
	8,  // 1: moego.api.payment.v2.RefundResult.refund_view:type_name -> moego.models.payment.v2.RefundView
	8,  // 2: moego.api.payment.v2.GetRefundResult.refund_view:type_name -> moego.models.payment.v2.RefundView
	9,  // 3: moego.api.payment.v2.ListRefundParams.pagination_request:type_name -> moego.utils.v2.PaginationRequest
	6,  // 4: moego.api.payment.v2.ListRefundParams.filter:type_name -> moego.api.payment.v2.ListRefundParams.Filter
	10, // 5: moego.api.payment.v2.ListRefundResult.pagination_request:type_name -> moego.utils.v2.PaginationResponse
	8,  // 6: moego.api.payment.v2.ListRefundResult.refund_views:type_name -> moego.models.payment.v2.RefundView
	11, // 7: moego.api.payment.v2.ListRefundParams.Filter.time_period:type_name -> moego.utils.v1.TimePeriod
	0,  // 8: moego.api.payment.v2.RefundService.Refund:input_type -> moego.api.payment.v2.RefundParams
	2,  // 9: moego.api.payment.v2.RefundService.GetRefund:input_type -> moego.api.payment.v2.GetRefundParams
	4,  // 10: moego.api.payment.v2.RefundService.ListRefund:input_type -> moego.api.payment.v2.ListRefundParams
	1,  // 11: moego.api.payment.v2.RefundService.Refund:output_type -> moego.api.payment.v2.RefundResult
	3,  // 12: moego.api.payment.v2.RefundService.GetRefund:output_type -> moego.api.payment.v2.GetRefundResult
	5,  // 13: moego.api.payment.v2.RefundService.ListRefund:output_type -> moego.api.payment.v2.ListRefundResult
	11, // [11:14] is the sub-list for method output_type
	8,  // [8:11] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_payment_v2_payment_refund_api_proto_init() }
func file_moego_api_payment_v2_payment_refund_api_proto_init() {
	if File_moego_api_payment_v2_payment_refund_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRefundParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRefundResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_refund_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRefundParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_payment_v2_payment_refund_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_payment_v2_payment_refund_api_proto_goTypes,
		DependencyIndexes: file_moego_api_payment_v2_payment_refund_api_proto_depIdxs,
		MessageInfos:      file_moego_api_payment_v2_payment_refund_api_proto_msgTypes,
	}.Build()
	File_moego_api_payment_v2_payment_refund_api_proto = out.File
	file_moego_api_payment_v2_payment_refund_api_proto_rawDesc = nil
	file_moego_api_payment_v2_payment_refund_api_proto_goTypes = nil
	file_moego_api_payment_v2_payment_refund_api_proto_depIdxs = nil
}
