// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/payment/v2/payment_refund_api.proto

package paymentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RefundServiceClient is the client API for RefundService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RefundServiceClient interface {
	// refund 给前端直接调用的退款
	Refund(ctx context.Context, in *RefundParams, opts ...grpc.CallOption) (*RefundResult, error)
	// 查询refund
	GetRefund(ctx context.Context, in *GetRefundParams, opts ...grpc.CallOption) (*GetRefundResult, error)
	// 获取退款列表
	ListRefund(ctx context.Context, in *ListRefundParams, opts ...grpc.CallOption) (*ListRefundResult, error)
}

type refundServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRefundServiceClient(cc grpc.ClientConnInterface) RefundServiceClient {
	return &refundServiceClient{cc}
}

func (c *refundServiceClient) Refund(ctx context.Context, in *RefundParams, opts ...grpc.CallOption) (*RefundResult, error) {
	out := new(RefundResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.RefundService/Refund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) GetRefund(ctx context.Context, in *GetRefundParams, opts ...grpc.CallOption) (*GetRefundResult, error) {
	out := new(GetRefundResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.RefundService/GetRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) ListRefund(ctx context.Context, in *ListRefundParams, opts ...grpc.CallOption) (*ListRefundResult, error) {
	out := new(ListRefundResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.RefundService/ListRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RefundServiceServer is the server API for RefundService service.
// All implementations must embed UnimplementedRefundServiceServer
// for forward compatibility
type RefundServiceServer interface {
	// refund 给前端直接调用的退款
	Refund(context.Context, *RefundParams) (*RefundResult, error)
	// 查询refund
	GetRefund(context.Context, *GetRefundParams) (*GetRefundResult, error)
	// 获取退款列表
	ListRefund(context.Context, *ListRefundParams) (*ListRefundResult, error)
	mustEmbedUnimplementedRefundServiceServer()
}

// UnimplementedRefundServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRefundServiceServer struct {
}

func (UnimplementedRefundServiceServer) Refund(context.Context, *RefundParams) (*RefundResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedRefundServiceServer) GetRefund(context.Context, *GetRefundParams) (*GetRefundResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRefund not implemented")
}
func (UnimplementedRefundServiceServer) ListRefund(context.Context, *ListRefundParams) (*ListRefundResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRefund not implemented")
}
func (UnimplementedRefundServiceServer) mustEmbedUnimplementedRefundServiceServer() {}

// UnsafeRefundServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RefundServiceServer will
// result in compilation errors.
type UnsafeRefundServiceServer interface {
	mustEmbedUnimplementedRefundServiceServer()
}

func RegisterRefundServiceServer(s grpc.ServiceRegistrar, srv RefundServiceServer) {
	s.RegisterService(&RefundService_ServiceDesc, srv)
}

func _RefundService_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.RefundService/Refund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).Refund(ctx, req.(*RefundParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_GetRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRefundParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).GetRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.RefundService/GetRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).GetRefund(ctx, req.(*GetRefundParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_ListRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRefundParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).ListRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.RefundService/ListRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).ListRefund(ctx, req.(*ListRefundParams))
	}
	return interceptor(ctx, in, info, handler)
}

// RefundService_ServiceDesc is the grpc.ServiceDesc for RefundService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RefundService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.payment.v2.RefundService",
	HandlerType: (*RefundServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Refund",
			Handler:    _RefundService_Refund_Handler,
		},
		{
			MethodName: "GetRefund",
			Handler:    _RefundService_GetRefund_Handler,
		},
		{
			MethodName: "ListRefund",
			Handler:    _RefundService_ListRefund_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/payment/v2/payment_refund_api.proto",
}
