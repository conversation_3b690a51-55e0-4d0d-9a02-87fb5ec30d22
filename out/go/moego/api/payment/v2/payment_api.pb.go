// (-- api-linter: core::0131::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0132::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0132::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/payment/v2/payment_api.proto

package paymentapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get payment version params
type GetPaymentVersionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPaymentVersionParams) Reset() {
	*x = GetPaymentVersionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentVersionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentVersionParams) ProtoMessage() {}

func (x *GetPaymentVersionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentVersionParams.ProtoReflect.Descriptor instead.
func (*GetPaymentVersionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{0}
}

// get payment version result
type GetPaymentVersionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付版本
	PaymentVersion v2.PaymentVersion `protobuf:"varint,1,opt,name=payment_version,json=paymentVersion,proto3,enum=moego.models.payment.v2.PaymentVersion" json:"payment_version,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetPaymentVersionResult) Reset() {
	*x = GetPaymentVersionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentVersionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentVersionResult) ProtoMessage() {}

func (x *GetPaymentVersionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentVersionResult.ProtoReflect.Descriptor instead.
func (*GetPaymentVersionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetPaymentVersionResult) GetPaymentVersion() v2.PaymentVersion {
	if x != nil {
		return x.PaymentVersion
	}
	return v2.PaymentVersion(0)
}

func (x *GetPaymentVersionResult) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// get pay data params
type GetPayDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道，可不传，将由后端根据渠道路由自行决定；如果传了，优先级高于后端路由
	ChannelType *v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType,oneof" json:"channel_type,omitempty"`
}

func (x *GetPayDataParams) Reset() {
	*x = GetPayDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataParams) ProtoMessage() {}

func (x *GetPayDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataParams.ProtoReflect.Descriptor instead.
func (*GetPayDataParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetPayDataParams) GetChannelType() v2.ChannelType {
	if x != nil && x.ChannelType != nil {
		return *x.ChannelType
	}
	return v2.ChannelType(0)
}

// get pay data result
type GetPayDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 支付数据
	//
	// Types that are assignable to Data:
	//
	//	*GetPayDataResult_AdyenData_
	Data isGetPayDataResult_Data `protobuf_oneof:"data"`
}

func (x *GetPayDataResult) Reset() {
	*x = GetPayDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataResult) ProtoMessage() {}

func (x *GetPayDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataResult.ProtoReflect.Descriptor instead.
func (*GetPayDataResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetPayDataResult) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (m *GetPayDataResult) GetData() isGetPayDataResult_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *GetPayDataResult) GetAdyenData() *GetPayDataResult_AdyenData {
	if x, ok := x.GetData().(*GetPayDataResult_AdyenData_); ok {
		return x.AdyenData
	}
	return nil
}

type isGetPayDataResult_Data interface {
	isGetPayDataResult_Data()
}

type GetPayDataResult_AdyenData_ struct {
	// adyen data
	AdyenData *GetPayDataResult_AdyenData `protobuf:"bytes,2,opt,name=adyen_data,json=adyenData,proto3,oneof"`
}

func (*GetPayDataResult_AdyenData_) isGetPayDataResult_Data() {}

// submit action detail params
type SubmitActionDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据id
	PaymentId int64 `protobuf:"varint,1,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// Action Result，前端从组件拿到的原始数据，
	// e.g. ayden 3ds2:
	//
	//	`{
	//	  "details": {
	//	    "threeDSResult": "eyJ0cmFuc1N0YXR1cyI6IlkifQ=="
	//	  }
	//	}`
	RawActionResult string `protobuf:"bytes,2,opt,name=raw_action_result,json=rawActionResult,proto3" json:"raw_action_result,omitempty"`
}

func (x *SubmitActionDetailParams) Reset() {
	*x = SubmitActionDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitActionDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitActionDetailParams) ProtoMessage() {}

func (x *SubmitActionDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitActionDetailParams.ProtoReflect.Descriptor instead.
func (*SubmitActionDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{4}
}

func (x *SubmitActionDetailParams) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *SubmitActionDetailParams) GetRawActionResult() string {
	if x != nil {
		return x.RawActionResult
	}
	return ""
}

// submit action result
type SubmitActionDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg 可以展示给用户的信息
	Msg *string `protobuf:"bytes,1,opt,name=msg,proto3,oneof" json:"msg,omitempty"`
	// channel response，渠道返回的原始数据，用于前端加载第三方支付组件,
	// e.g. adyen:
	//
	//	`{
	//	  "resultCode": "Authorised",
	//	  "pspReference": "V4HZ4RBFJGXXGN82"
	//	}`
	RawChannelResponse string `protobuf:"bytes,2,opt,name=raw_channel_response,json=rawChannelResponse,proto3" json:"raw_channel_response,omitempty"`
}

func (x *SubmitActionDetailResult) Reset() {
	*x = SubmitActionDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitActionDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitActionDetailResult) ProtoMessage() {}

func (x *SubmitActionDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitActionDetailResult.ProtoReflect.Descriptor instead.
func (*SubmitActionDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{5}
}

func (x *SubmitActionDetailResult) GetMsg() string {
	if x != nil && x.Msg != nil {
		return *x.Msg
	}
	return ""
}

func (x *SubmitActionDetailResult) GetRawChannelResponse() string {
	if x != nil {
		return x.RawChannelResponse
	}
	return ""
}

// get payment params
type GetPaymentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据id
	PaymentId int64 `protobuf:"varint,1,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
}

func (x *GetPaymentParams) Reset() {
	*x = GetPaymentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentParams) ProtoMessage() {}

func (x *GetPaymentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentParams.ProtoReflect.Descriptor instead.
func (*GetPaymentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetPaymentParams) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

// get payment result
type GetPaymentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment view
	Payment *v2.PaymentView `protobuf:"bytes,1,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *GetPaymentResult) Reset() {
	*x = GetPaymentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentResult) ProtoMessage() {}

func (x *GetPaymentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentResult.ProtoReflect.Descriptor instead.
func (*GetPaymentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetPaymentResult) GetPayment() *v2.PaymentView {
	if x != nil {
		return x.Payment
	}
	return nil
}

// 查询payment列表请求参数
type ListPaymentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页查询请求
	PaginationRequest *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// filter
	Filter *ListPaymentParams_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPaymentParams) Reset() {
	*x = ListPaymentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentParams) ProtoMessage() {}

func (x *ListPaymentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentParams.ProtoReflect.Descriptor instead.
func (*ListPaymentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListPaymentParams) GetPaginationRequest() *v21.PaginationRequest {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListPaymentParams) GetFilter() *ListPaymentParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list payment result
type ListPaymentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付列表
	Payments []*v2.PaymentView `protobuf:"bytes,1,rep,name=payments,proto3" json:"payments,omitempty"`
	// 分页
	PaginationRequest *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
}

func (x *ListPaymentResult) Reset() {
	*x = ListPaymentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentResult) ProtoMessage() {}

func (x *ListPaymentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentResult.ProtoReflect.Descriptor instead.
func (*ListPaymentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListPaymentResult) GetPayments() []*v2.PaymentView {
	if x != nil {
		return x.Payments
	}
	return nil
}

func (x *ListPaymentResult) GetPaginationRequest() *v21.PaginationResponse {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

// add recurring payment method params
type AddRecurringPaymentMethodParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId *int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// customer code
	EncryptedCustomerId *string `protobuf:"bytes,2,opt,name=encrypted_customer_id,json=encryptedCustomerId,proto3,oneof" json:"encrypted_customer_id,omitempty"`
	// channel type
	ChannelType *v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType,oneof" json:"channel_type,omitempty"`
	// payment method type，目前只能是 Card
	PaymentMethodType v2.PaymentMethod_MethodType `protobuf:"varint,4,opt,name=payment_method_type,json=paymentMethodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"payment_method_type,omitempty"`
	// 要存储的支付方式
	Detail *v2.PaymentMethod_Detail `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`
	// 透传参数，一般是用户自定义的额外信息
	Extra *v2.RecurringPaymentMethodModel_Extra `protobuf:"bytes,6,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
}

func (x *AddRecurringPaymentMethodParams) Reset() {
	*x = AddRecurringPaymentMethodParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRecurringPaymentMethodParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRecurringPaymentMethodParams) ProtoMessage() {}

func (x *AddRecurringPaymentMethodParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRecurringPaymentMethodParams.ProtoReflect.Descriptor instead.
func (*AddRecurringPaymentMethodParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{10}
}

func (x *AddRecurringPaymentMethodParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *AddRecurringPaymentMethodParams) GetEncryptedCustomerId() string {
	if x != nil && x.EncryptedCustomerId != nil {
		return *x.EncryptedCustomerId
	}
	return ""
}

func (x *AddRecurringPaymentMethodParams) GetChannelType() v2.ChannelType {
	if x != nil && x.ChannelType != nil {
		return *x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *AddRecurringPaymentMethodParams) GetPaymentMethodType() v2.PaymentMethod_MethodType {
	if x != nil {
		return x.PaymentMethodType
	}
	return v2.PaymentMethod_MethodType(0)
}

func (x *AddRecurringPaymentMethodParams) GetDetail() *v2.PaymentMethod_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *AddRecurringPaymentMethodParams) GetExtra() *v2.RecurringPaymentMethodModel_Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

// add recurring payment method result
type AddRecurringPaymentMethodResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已存储的 payment method
	RecurringPaymentMethodView *v2.RecurringPaymentMethodView `protobuf:"bytes,1,opt,name=recurring_payment_method_view,json=recurringPaymentMethodView,proto3" json:"recurring_payment_method_view,omitempty"`
}

func (x *AddRecurringPaymentMethodResult) Reset() {
	*x = AddRecurringPaymentMethodResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRecurringPaymentMethodResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRecurringPaymentMethodResult) ProtoMessage() {}

func (x *AddRecurringPaymentMethodResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRecurringPaymentMethodResult.ProtoReflect.Descriptor instead.
func (*AddRecurringPaymentMethodResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{11}
}

func (x *AddRecurringPaymentMethodResult) GetRecurringPaymentMethodView() *v2.RecurringPaymentMethodView {
	if x != nil {
		return x.RecurringPaymentMethodView
	}
	return nil
}

// delete recurring payment method params
type DeleteRecurringPaymentMethodParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 存储的 payment method id
	PaymentMethodId int64 `protobuf:"varint,1,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *DeleteRecurringPaymentMethodParams) Reset() {
	*x = DeleteRecurringPaymentMethodParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecurringPaymentMethodParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecurringPaymentMethodParams) ProtoMessage() {}

func (x *DeleteRecurringPaymentMethodParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecurringPaymentMethodParams.ProtoReflect.Descriptor instead.
func (*DeleteRecurringPaymentMethodParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteRecurringPaymentMethodParams) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

// delete recurring payment method result
type DeleteRecurringPaymentMethodResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRecurringPaymentMethodResult) Reset() {
	*x = DeleteRecurringPaymentMethodResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecurringPaymentMethodResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecurringPaymentMethodResult) ProtoMessage() {}

func (x *DeleteRecurringPaymentMethodResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecurringPaymentMethodResult.ProtoReflect.Descriptor instead.
func (*DeleteRecurringPaymentMethodResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{13}
}

// set recurring payment method primary params
type SetRecurringPaymentMethodPrimaryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 存储的payment method id
	PaymentMethodId int64 `protobuf:"varint,1,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *SetRecurringPaymentMethodPrimaryParams) Reset() {
	*x = SetRecurringPaymentMethodPrimaryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecurringPaymentMethodPrimaryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecurringPaymentMethodPrimaryParams) ProtoMessage() {}

func (x *SetRecurringPaymentMethodPrimaryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecurringPaymentMethodPrimaryParams.ProtoReflect.Descriptor instead.
func (*SetRecurringPaymentMethodPrimaryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{14}
}

func (x *SetRecurringPaymentMethodPrimaryParams) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

// set recurring payment method primary result
type SetRecurringPaymentMethodPrimaryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已存储的 payment method
	RecurringPaymentMethodView *v2.RecurringPaymentMethodView `protobuf:"bytes,1,opt,name=recurring_payment_method_view,json=recurringPaymentMethodView,proto3" json:"recurring_payment_method_view,omitempty"`
}

func (x *SetRecurringPaymentMethodPrimaryResult) Reset() {
	*x = SetRecurringPaymentMethodPrimaryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecurringPaymentMethodPrimaryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecurringPaymentMethodPrimaryResult) ProtoMessage() {}

func (x *SetRecurringPaymentMethodPrimaryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecurringPaymentMethodPrimaryResult.ProtoReflect.Descriptor instead.
func (*SetRecurringPaymentMethodPrimaryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{15}
}

func (x *SetRecurringPaymentMethodPrimaryResult) GetRecurringPaymentMethodView() *v2.RecurringPaymentMethodView {
	if x != nil {
		return x.RecurringPaymentMethodView
	}
	return nil
}

// list recurring payment method params
type ListRecurringPaymentMethodsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *ListRecurringPaymentMethodsParams) Reset() {
	*x = ListRecurringPaymentMethodsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsParams) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsParams.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{16}
}

func (x *ListRecurringPaymentMethodsParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// list recurring payment method result
type ListRecurringPaymentMethodsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已存储的 payment method 列表
	RecurringPaymentMethodViews []*v2.RecurringPaymentMethodView `protobuf:"bytes,1,rep,name=recurring_payment_method_views,json=recurringPaymentMethodViews,proto3" json:"recurring_payment_method_views,omitempty"`
}

func (x *ListRecurringPaymentMethodsResult) Reset() {
	*x = ListRecurringPaymentMethodsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsResult) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsResult.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{17}
}

func (x *ListRecurringPaymentMethodsResult) GetRecurringPaymentMethodViews() []*v2.RecurringPaymentMethodView {
	if x != nil {
		return x.RecurringPaymentMethodViews
	}
	return nil
}

// adyen data
type GetPayDataResult_AdyenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data
	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetPayDataResult_AdyenData) Reset() {
	*x = GetPayDataResult_AdyenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataResult_AdyenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataResult_AdyenData) ProtoMessage() {}

func (x *GetPayDataResult_AdyenData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataResult_AdyenData.ProtoReflect.Descriptor instead.
func (*GetPayDataResult_AdyenData) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetPayDataResult_AdyenData) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// filter
type ListPaymentParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// order id
	OrderIds []int64 `protobuf:"varint,2,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// order payment id
	OrderPaymentIds []int64 `protobuf:"varint,3,rep,packed,name=order_payment_ids,json=orderPaymentIds,proto3" json:"order_payment_ids,omitempty"`
	// payment id
	PaymentIds []int64 `protobuf:"varint,4,rep,packed,name=payment_ids,json=paymentIds,proto3" json:"payment_ids,omitempty"`
	// 查询时间范围
	TimePeriod *v1.TimePeriod `protobuf:"bytes,5,opt,name=time_period,json=timePeriod,proto3" json:"time_period,omitempty"`
}

func (x *ListPaymentParams_Filter) Reset() {
	*x = ListPaymentParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentParams_Filter) ProtoMessage() {}

func (x *ListPaymentParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentParams_Filter.ProtoReflect.Descriptor instead.
func (*ListPaymentParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListPaymentParams_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListPaymentParams_Filter) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *ListPaymentParams_Filter) GetOrderPaymentIds() []int64 {
	if x != nil {
		return x.OrderPaymentIds
	}
	return nil
}

func (x *ListPaymentParams_Filter) GetPaymentIds() []int64 {
	if x != nil {
		return x.PaymentIds
	}
	return nil
}

func (x *ListPaymentParams_Filter) GetTimePeriod() *v1.TimePeriod {
	if x != nil {
		return x.TimePeriod
	}
	return nil
}

var File_moego_api_payment_v2_payment_api_proto protoreflect.FileDescriptor

var file_moego_api_payment_v2_payment_api_proto_rawDesc = []byte{
	0x0a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x2a,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xb4, 0x01, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x71, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74,
	0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4c, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xd7, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x47, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x0a, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x61, 0x64,
	0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x1f, 0x0a, 0x09, 0x41, 0x64, 0x79, 0x65, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x65, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72,
	0x61, 0x77, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x61, 0x77, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x6b, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x15, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x61,
	0x77, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x61, 0x77, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x06, 0x0a, 0x04,
	0x5f, 0x6d, 0x73, 0x67, 0x22, 0x31, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x52, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x07, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x82, 0x03, 0x0a, 0x11,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x50, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xd2, 0x01, 0x0a, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x22, 0xa8, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x94, 0x04, 0x0a, 0x1f,
	0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x24, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x13, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4c,
	0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x13,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x45, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x55, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x48, 0x03, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x18, 0x0a,
	0x16, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x22, 0x99, 0x01, 0x0a, 0x1f, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x1d, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x1a, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x56, 0x69, 0x65, 0x77, 0x22, 0x50,
	0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64,
	0x22, 0x24, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x54, 0x0a, 0x26, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x22, 0xa0, 0x01, 0x0a,
	0x26, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x1d, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x1a, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x56, 0x69, 0x65, 0x77, 0x22,
	0x44, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x78, 0x0a, 0x1e, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x56, 0x69, 0x65, 0x77, 0x52, 0x1b, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x56, 0x69, 0x65, 0x77, 0x73, 0x32, 0xea, 0x08, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x0a, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x74, 0x0a, 0x12, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x5c, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f, 0x0a,
	0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x89,
	0x01, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x92, 0x01, 0x0a, 0x1c, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x38, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x9e, 0x01, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x8f, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73,
	0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x78, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_payment_v2_payment_api_proto_rawDescOnce sync.Once
	file_moego_api_payment_v2_payment_api_proto_rawDescData = file_moego_api_payment_v2_payment_api_proto_rawDesc
)

func file_moego_api_payment_v2_payment_api_proto_rawDescGZIP() []byte {
	file_moego_api_payment_v2_payment_api_proto_rawDescOnce.Do(func() {
		file_moego_api_payment_v2_payment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_payment_v2_payment_api_proto_rawDescData)
	})
	return file_moego_api_payment_v2_payment_api_proto_rawDescData
}

var file_moego_api_payment_v2_payment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_moego_api_payment_v2_payment_api_proto_goTypes = []interface{}{
	(*GetPaymentVersionParams)(nil),                // 0: moego.api.payment.v2.GetPaymentVersionParams
	(*GetPaymentVersionResult)(nil),                // 1: moego.api.payment.v2.GetPaymentVersionResult
	(*GetPayDataParams)(nil),                       // 2: moego.api.payment.v2.GetPayDataParams
	(*GetPayDataResult)(nil),                       // 3: moego.api.payment.v2.GetPayDataResult
	(*SubmitActionDetailParams)(nil),               // 4: moego.api.payment.v2.SubmitActionDetailParams
	(*SubmitActionDetailResult)(nil),               // 5: moego.api.payment.v2.SubmitActionDetailResult
	(*GetPaymentParams)(nil),                       // 6: moego.api.payment.v2.GetPaymentParams
	(*GetPaymentResult)(nil),                       // 7: moego.api.payment.v2.GetPaymentResult
	(*ListPaymentParams)(nil),                      // 8: moego.api.payment.v2.ListPaymentParams
	(*ListPaymentResult)(nil),                      // 9: moego.api.payment.v2.ListPaymentResult
	(*AddRecurringPaymentMethodParams)(nil),        // 10: moego.api.payment.v2.AddRecurringPaymentMethodParams
	(*AddRecurringPaymentMethodResult)(nil),        // 11: moego.api.payment.v2.AddRecurringPaymentMethodResult
	(*DeleteRecurringPaymentMethodParams)(nil),     // 12: moego.api.payment.v2.DeleteRecurringPaymentMethodParams
	(*DeleteRecurringPaymentMethodResult)(nil),     // 13: moego.api.payment.v2.DeleteRecurringPaymentMethodResult
	(*SetRecurringPaymentMethodPrimaryParams)(nil), // 14: moego.api.payment.v2.SetRecurringPaymentMethodPrimaryParams
	(*SetRecurringPaymentMethodPrimaryResult)(nil), // 15: moego.api.payment.v2.SetRecurringPaymentMethodPrimaryResult
	(*ListRecurringPaymentMethodsParams)(nil),      // 16: moego.api.payment.v2.ListRecurringPaymentMethodsParams
	(*ListRecurringPaymentMethodsResult)(nil),      // 17: moego.api.payment.v2.ListRecurringPaymentMethodsResult
	(*GetPayDataResult_AdyenData)(nil),             // 18: moego.api.payment.v2.GetPayDataResult.AdyenData
	(*ListPaymentParams_Filter)(nil),               // 19: moego.api.payment.v2.ListPaymentParams.Filter
	(v2.PaymentVersion)(0),                         // 20: moego.models.payment.v2.PaymentVersion
	(v2.ChannelType)(0),                            // 21: moego.models.payment.v2.ChannelType
	(*v2.PaymentView)(nil),                         // 22: moego.models.payment.v2.PaymentView
	(*v21.PaginationRequest)(nil),                  // 23: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),                 // 24: moego.utils.v2.PaginationResponse
	(v2.PaymentMethod_MethodType)(0),               // 25: moego.models.payment.v2.PaymentMethod.MethodType
	(*v2.PaymentMethod_Detail)(nil),                // 26: moego.models.payment.v2.PaymentMethod.Detail
	(*v2.RecurringPaymentMethodModel_Extra)(nil),   // 27: moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	(*v2.RecurringPaymentMethodView)(nil),          // 28: moego.models.payment.v2.RecurringPaymentMethodView
	(*v1.TimePeriod)(nil),                          // 29: moego.utils.v1.TimePeriod
}
var file_moego_api_payment_v2_payment_api_proto_depIdxs = []int32{
	20, // 0: moego.api.payment.v2.GetPaymentVersionResult.payment_version:type_name -> moego.models.payment.v2.PaymentVersion
	21, // 1: moego.api.payment.v2.GetPaymentVersionResult.channel_type:type_name -> moego.models.payment.v2.ChannelType
	21, // 2: moego.api.payment.v2.GetPayDataParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	21, // 3: moego.api.payment.v2.GetPayDataResult.channel_type:type_name -> moego.models.payment.v2.ChannelType
	18, // 4: moego.api.payment.v2.GetPayDataResult.adyen_data:type_name -> moego.api.payment.v2.GetPayDataResult.AdyenData
	22, // 5: moego.api.payment.v2.GetPaymentResult.payment:type_name -> moego.models.payment.v2.PaymentView
	23, // 6: moego.api.payment.v2.ListPaymentParams.pagination_request:type_name -> moego.utils.v2.PaginationRequest
	19, // 7: moego.api.payment.v2.ListPaymentParams.filter:type_name -> moego.api.payment.v2.ListPaymentParams.Filter
	22, // 8: moego.api.payment.v2.ListPaymentResult.payments:type_name -> moego.models.payment.v2.PaymentView
	24, // 9: moego.api.payment.v2.ListPaymentResult.pagination_request:type_name -> moego.utils.v2.PaginationResponse
	21, // 10: moego.api.payment.v2.AddRecurringPaymentMethodParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	25, // 11: moego.api.payment.v2.AddRecurringPaymentMethodParams.payment_method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	26, // 12: moego.api.payment.v2.AddRecurringPaymentMethodParams.detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	27, // 13: moego.api.payment.v2.AddRecurringPaymentMethodParams.extra:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	28, // 14: moego.api.payment.v2.AddRecurringPaymentMethodResult.recurring_payment_method_view:type_name -> moego.models.payment.v2.RecurringPaymentMethodView
	28, // 15: moego.api.payment.v2.SetRecurringPaymentMethodPrimaryResult.recurring_payment_method_view:type_name -> moego.models.payment.v2.RecurringPaymentMethodView
	28, // 16: moego.api.payment.v2.ListRecurringPaymentMethodsResult.recurring_payment_method_views:type_name -> moego.models.payment.v2.RecurringPaymentMethodView
	29, // 17: moego.api.payment.v2.ListPaymentParams.Filter.time_period:type_name -> moego.utils.v1.TimePeriod
	0,  // 18: moego.api.payment.v2.PaymentService.GetPaymentVersion:input_type -> moego.api.payment.v2.GetPaymentVersionParams
	2,  // 19: moego.api.payment.v2.PaymentService.GetPayData:input_type -> moego.api.payment.v2.GetPayDataParams
	4,  // 20: moego.api.payment.v2.PaymentService.SubmitActionDetail:input_type -> moego.api.payment.v2.SubmitActionDetailParams
	6,  // 21: moego.api.payment.v2.PaymentService.GetPayment:input_type -> moego.api.payment.v2.GetPaymentParams
	8,  // 22: moego.api.payment.v2.PaymentService.ListPayment:input_type -> moego.api.payment.v2.ListPaymentParams
	10, // 23: moego.api.payment.v2.PaymentService.AddRecurringPaymentMethod:input_type -> moego.api.payment.v2.AddRecurringPaymentMethodParams
	12, // 24: moego.api.payment.v2.PaymentService.DeleteRecurringPaymentMethod:input_type -> moego.api.payment.v2.DeleteRecurringPaymentMethodParams
	14, // 25: moego.api.payment.v2.PaymentService.SetRecurringPaymentMethodPrimary:input_type -> moego.api.payment.v2.SetRecurringPaymentMethodPrimaryParams
	16, // 26: moego.api.payment.v2.PaymentService.ListRecurringPaymentMethods:input_type -> moego.api.payment.v2.ListRecurringPaymentMethodsParams
	1,  // 27: moego.api.payment.v2.PaymentService.GetPaymentVersion:output_type -> moego.api.payment.v2.GetPaymentVersionResult
	3,  // 28: moego.api.payment.v2.PaymentService.GetPayData:output_type -> moego.api.payment.v2.GetPayDataResult
	5,  // 29: moego.api.payment.v2.PaymentService.SubmitActionDetail:output_type -> moego.api.payment.v2.SubmitActionDetailResult
	7,  // 30: moego.api.payment.v2.PaymentService.GetPayment:output_type -> moego.api.payment.v2.GetPaymentResult
	9,  // 31: moego.api.payment.v2.PaymentService.ListPayment:output_type -> moego.api.payment.v2.ListPaymentResult
	11, // 32: moego.api.payment.v2.PaymentService.AddRecurringPaymentMethod:output_type -> moego.api.payment.v2.AddRecurringPaymentMethodResult
	13, // 33: moego.api.payment.v2.PaymentService.DeleteRecurringPaymentMethod:output_type -> moego.api.payment.v2.DeleteRecurringPaymentMethodResult
	15, // 34: moego.api.payment.v2.PaymentService.SetRecurringPaymentMethodPrimary:output_type -> moego.api.payment.v2.SetRecurringPaymentMethodPrimaryResult
	17, // 35: moego.api.payment.v2.PaymentService.ListRecurringPaymentMethods:output_type -> moego.api.payment.v2.ListRecurringPaymentMethodsResult
	27, // [27:36] is the sub-list for method output_type
	18, // [18:27] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_moego_api_payment_v2_payment_api_proto_init() }
func file_moego_api_payment_v2_payment_api_proto_init() {
	if File_moego_api_payment_v2_payment_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_payment_v2_payment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentVersionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentVersionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitActionDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitActionDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRecurringPaymentMethodParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRecurringPaymentMethodResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecurringPaymentMethodParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecurringPaymentMethodResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecurringPaymentMethodPrimaryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecurringPaymentMethodPrimaryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataResult_AdyenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_payment_v2_payment_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_payment_v2_payment_api_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*GetPayDataResult_AdyenData_)(nil),
	}
	file_moego_api_payment_v2_payment_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_api_payment_v2_payment_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_payment_v2_payment_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_payment_v2_payment_api_proto_goTypes,
		DependencyIndexes: file_moego_api_payment_v2_payment_api_proto_depIdxs,
		MessageInfos:      file_moego_api_payment_v2_payment_api_proto_msgTypes,
	}.Build()
	File_moego_api_payment_v2_payment_api_proto = out.File
	file_moego_api_payment_v2_payment_api_proto_rawDesc = nil
	file_moego_api_payment_v2_payment_api_proto_goTypes = nil
	file_moego_api_payment_v2_payment_api_proto_depIdxs = nil
}
