// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/payment/v2/payment_onboard_api.proto

package paymentapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for GetOnboard
type GetOnboardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道，先强制要求传，固定传 Adyen
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetOnboardParams) Reset() {
	*x = GetOnboardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardParams) ProtoMessage() {}

func (x *GetOnboardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardParams.ProtoReflect.Descriptor instead.
func (*GetOnboardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetOnboardParams) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetOnboard
type GetOnboardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Onboard 状态：
	// - INITIAL：初始状态
	// - CONFIGURED：配置完毕，onboarding 中
	// - ONBOARDED：已完成 onboarding，待用户确认
	// - FINISHED：完成 onboarding，包括后续有 upcoming verification 的状态
	// - NOT_APPLICABLE：不适用，预留的状态，目前的接口设计不会返回这种状态
	Status v2.OnboardStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.payment.v2.OnboardStatus" json:"status,omitempty"`
	// 包含的步骤，status 为 CONFIGURED 时有效。
	Steps []*v2.OnboardStep `protobuf:"bytes,2,rep,name=steps,proto3" json:"steps,omitempty"`
	// 当前步骤下标（相对于 steps 字段）
	CurrentStep int32 `protobuf:"varint,3,opt,name=current_step,json=currentStep,proto3" json:"current_step,omitempty"`
}

func (x *GetOnboardResult) Reset() {
	*x = GetOnboardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardResult) ProtoMessage() {}

func (x *GetOnboardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardResult.ProtoReflect.Descriptor instead.
func (*GetOnboardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetOnboardResult) GetStatus() v2.OnboardStatus {
	if x != nil {
		return x.Status
	}
	return v2.OnboardStatus(0)
}

func (x *GetOnboardResult) GetSteps() []*v2.OnboardStep {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *GetOnboardResult) GetCurrentStep() int32 {
	if x != nil {
		return x.CurrentStep
	}
	return 0
}

// Request for ProceedOnboard
type ProceedOnboardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 链接内完成后跳回的地址
	ReturnUrl *string `protobuf:"bytes,2,opt,name=return_url,json=returnUrl,proto3,oneof" json:"return_url,omitempty"`
	// 在 configure 之前需要提供的渠道特定的数据。
	//
	// Types that are assignable to Params:
	//
	//	*ProceedOnboardParams_AdyenPreConfig
	Params isProceedOnboardParams_Params `protobuf_oneof:"params"`
}

func (x *ProceedOnboardParams) Reset() {
	*x = ProceedOnboardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProceedOnboardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProceedOnboardParams) ProtoMessage() {}

func (x *ProceedOnboardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProceedOnboardParams.ProtoReflect.Descriptor instead.
func (*ProceedOnboardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{2}
}

func (x *ProceedOnboardParams) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *ProceedOnboardParams) GetReturnUrl() string {
	if x != nil && x.ReturnUrl != nil {
		return *x.ReturnUrl
	}
	return ""
}

func (m *ProceedOnboardParams) GetParams() isProceedOnboardParams_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *ProceedOnboardParams) GetAdyenPreConfig() *v2.AdyenPreConfigDef {
	if x, ok := x.GetParams().(*ProceedOnboardParams_AdyenPreConfig); ok {
		return x.AdyenPreConfig
	}
	return nil
}

type isProceedOnboardParams_Params interface {
	isProceedOnboardParams_Params()
}

type ProceedOnboardParams_AdyenPreConfig struct {
	// Adyen 的 pre configuration，只有 OnboardStatus 为 INITIAL 状态时会被使用。
	AdyenPreConfig *v2.AdyenPreConfigDef `protobuf:"bytes,3,opt,name=adyen_pre_config,json=adyenPreConfig,proto3,oneof"`
}

func (*ProceedOnboardParams_AdyenPreConfig) isProceedOnboardParams_Params() {}

// Response for ProceedOnboard
type ProceedOnboardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链接，若有则需要跳转到该链接来推进 onboarding
	Url *string `protobuf:"bytes,1,opt,name=url,proto3,oneof" json:"url,omitempty"`
}

func (x *ProceedOnboardResult) Reset() {
	*x = ProceedOnboardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProceedOnboardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProceedOnboardResult) ProtoMessage() {}

func (x *ProceedOnboardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProceedOnboardResult.ProtoReflect.Descriptor instead.
func (*ProceedOnboardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{3}
}

func (x *ProceedOnboardResult) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

// Request for ConfirmOnboardFinished
type ConfirmOnboardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *ConfirmOnboardParams) Reset() {
	*x = ConfirmOnboardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmOnboardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmOnboardParams) ProtoMessage() {}

func (x *ConfirmOnboardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmOnboardParams.ProtoReflect.Descriptor instead.
func (*ConfirmOnboardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{4}
}

func (x *ConfirmOnboardParams) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for ConfirmOnboardFinished
type ConfirmOnboardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConfirmOnboardResult) Reset() {
	*x = ConfirmOnboardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmOnboardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmOnboardResult) ProtoMessage() {}

func (x *ConfirmOnboardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmOnboardResult.ProtoReflect.Descriptor instead.
func (*ConfirmOnboardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{5}
}

// Request for GetChannelAccountDetail
type GetChannelAccountDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetChannelAccountDetailParams) Reset() {
	*x = GetChannelAccountDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAccountDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAccountDetailParams) ProtoMessage() {}

func (x *GetChannelAccountDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAccountDetailParams.ProtoReflect.Descriptor instead.
func (*GetChannelAccountDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetChannelAccountDetailParams) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetChannelAccountDetail
type GetChannelAccountDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 账号的基本信息
	ChannelAccount *v2.ChannelAccount `protobuf:"bytes,1,opt,name=channel_account,json=channelAccount,proto3" json:"channel_account,omitempty"`
	// 需要关注的 KYC 验证信息
	Verifications []*v2.OnboardVerification `protobuf:"bytes,2,rep,name=verifications,proto3" json:"verifications,omitempty"`
	// Bank Accounts
	BankAccounts []*v2.BankAccount `protobuf:"bytes,3,rep,name=bank_accounts,json=bankAccounts,proto3" json:"bank_accounts,omitempty"`
}

func (x *GetChannelAccountDetailResult) Reset() {
	*x = GetChannelAccountDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAccountDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAccountDetailResult) ProtoMessage() {}

func (x *GetChannelAccountDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAccountDetailResult.ProtoReflect.Descriptor instead.
func (*GetChannelAccountDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetChannelAccountDetailResult) GetChannelAccount() *v2.ChannelAccount {
	if x != nil {
		return x.ChannelAccount
	}
	return nil
}

func (x *GetChannelAccountDetailResult) GetVerifications() []*v2.OnboardVerification {
	if x != nil {
		return x.Verifications
	}
	return nil
}

func (x *GetChannelAccountDetailResult) GetBankAccounts() []*v2.BankAccount {
	if x != nil {
		return x.BankAccounts
	}
	return nil
}

// Request for RequestOnboardUpdate
type RequestOnboardUpdateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 链接内完成后跳回的地址
	ReturnUrl *string `protobuf:"bytes,2,opt,name=return_url,json=returnUrl,proto3,oneof" json:"return_url,omitempty"`
	// 各个渠道可能需要的额外参数
	//
	// Types that are assignable to ChannelParams:
	//
	//	*RequestOnboardUpdateParams_Adyen
	ChannelParams isRequestOnboardUpdateParams_ChannelParams `protobuf_oneof:"channel_params"`
}

func (x *RequestOnboardUpdateParams) Reset() {
	*x = RequestOnboardUpdateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestOnboardUpdateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestOnboardUpdateParams) ProtoMessage() {}

func (x *RequestOnboardUpdateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestOnboardUpdateParams.ProtoReflect.Descriptor instead.
func (*RequestOnboardUpdateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{8}
}

func (x *RequestOnboardUpdateParams) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *RequestOnboardUpdateParams) GetReturnUrl() string {
	if x != nil && x.ReturnUrl != nil {
		return *x.ReturnUrl
	}
	return ""
}

func (m *RequestOnboardUpdateParams) GetChannelParams() isRequestOnboardUpdateParams_ChannelParams {
	if m != nil {
		return m.ChannelParams
	}
	return nil
}

func (x *RequestOnboardUpdateParams) GetAdyen() *RequestOnboardUpdateParams_AdyenParams {
	if x, ok := x.GetChannelParams().(*RequestOnboardUpdateParams_Adyen); ok {
		return x.Adyen
	}
	return nil
}

type isRequestOnboardUpdateParams_ChannelParams interface {
	isRequestOnboardUpdateParams_ChannelParams()
}

type RequestOnboardUpdateParams_Adyen struct {
	// Adyen
	Adyen *RequestOnboardUpdateParams_AdyenParams `protobuf:"bytes,3,opt,name=adyen,proto3,oneof"`
}

func (*RequestOnboardUpdateParams_Adyen) isRequestOnboardUpdateParams_ChannelParams() {}

// Response for RequestOnboardUpdate
type RequestOnboardUpdateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链接，若有则需要跳转到该链接来更新 onboard 信息
	Url *string `protobuf:"bytes,1,opt,name=url,proto3,oneof" json:"url,omitempty"`
}

func (x *RequestOnboardUpdateResult) Reset() {
	*x = RequestOnboardUpdateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestOnboardUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestOnboardUpdateResult) ProtoMessage() {}

func (x *RequestOnboardUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestOnboardUpdateResult.ProtoReflect.Descriptor instead.
func (*RequestOnboardUpdateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{9}
}

func (x *RequestOnboardUpdateResult) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

// Additional params for Adyen.
type RequestOnboardUpdateParams_AdyenParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Additional configuration for current business.
	BusinessConfig *v2.AdyenBusinessConfig `protobuf:"bytes,1,opt,name=business_config,json=businessConfig,proto3,oneof" json:"business_config,omitempty"`
}

func (x *RequestOnboardUpdateParams_AdyenParams) Reset() {
	*x = RequestOnboardUpdateParams_AdyenParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestOnboardUpdateParams_AdyenParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestOnboardUpdateParams_AdyenParams) ProtoMessage() {}

func (x *RequestOnboardUpdateParams_AdyenParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestOnboardUpdateParams_AdyenParams.ProtoReflect.Descriptor instead.
func (*RequestOnboardUpdateParams_AdyenParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *RequestOnboardUpdateParams_AdyenParams) GetBusinessConfig() *v2.AdyenBusinessConfig {
	if x != nil {
		return x.BusinessConfig
	}
	return nil
}

var File_moego_api_payment_v2_payment_onboard_api_proto protoreflect.FileDescriptor

var file_moego_api_payment_v2_payment_onboard_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5b, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x47, 0x0a,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x05, 0x73,
	0x74, 0x65, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x65, 0x70,
	0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x65, 0x70, 0x22, 0xf4, 0x01, 0x0a, 0x14, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a,
	0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01,
	0x12, 0x56, 0x0a, 0x10, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x50,
	0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72,
	0x6c, 0x22, 0x35, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x88, 0x01, 0x01,
	0x42, 0x06, 0x0a, 0x04, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x5f, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x68, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x90, 0x02, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a,
	0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x52, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0xff,
	0x02, 0x0a, 0x1a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x47, 0x0a,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x05, 0x61, 0x64,
	0x79, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x41, 0x64, 0x79, 0x65,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x05, 0x61, 0x64, 0x79, 0x65, 0x6e,
	0x1a, 0x7d, 0x0a, 0x0b, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x5a, 0x0a, 0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42,
	0x10, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x22, 0x3b, 0x0a, 0x1a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x15,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x75, 0x72, 0x6c, 0x32, 0xcb, 0x04,
	0x0a, 0x15, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5c, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x68, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x65, 0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x68, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x83, 0x01, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x7a, 0x0a, 0x14, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x78, 0x0a, 0x1c, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_payment_v2_payment_onboard_api_proto_rawDescOnce sync.Once
	file_moego_api_payment_v2_payment_onboard_api_proto_rawDescData = file_moego_api_payment_v2_payment_onboard_api_proto_rawDesc
)

func file_moego_api_payment_v2_payment_onboard_api_proto_rawDescGZIP() []byte {
	file_moego_api_payment_v2_payment_onboard_api_proto_rawDescOnce.Do(func() {
		file_moego_api_payment_v2_payment_onboard_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_payment_v2_payment_onboard_api_proto_rawDescData)
	})
	return file_moego_api_payment_v2_payment_onboard_api_proto_rawDescData
}

var file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_api_payment_v2_payment_onboard_api_proto_goTypes = []interface{}{
	(*GetOnboardParams)(nil),                       // 0: moego.api.payment.v2.GetOnboardParams
	(*GetOnboardResult)(nil),                       // 1: moego.api.payment.v2.GetOnboardResult
	(*ProceedOnboardParams)(nil),                   // 2: moego.api.payment.v2.ProceedOnboardParams
	(*ProceedOnboardResult)(nil),                   // 3: moego.api.payment.v2.ProceedOnboardResult
	(*ConfirmOnboardParams)(nil),                   // 4: moego.api.payment.v2.ConfirmOnboardParams
	(*ConfirmOnboardResult)(nil),                   // 5: moego.api.payment.v2.ConfirmOnboardResult
	(*GetChannelAccountDetailParams)(nil),          // 6: moego.api.payment.v2.GetChannelAccountDetailParams
	(*GetChannelAccountDetailResult)(nil),          // 7: moego.api.payment.v2.GetChannelAccountDetailResult
	(*RequestOnboardUpdateParams)(nil),             // 8: moego.api.payment.v2.RequestOnboardUpdateParams
	(*RequestOnboardUpdateResult)(nil),             // 9: moego.api.payment.v2.RequestOnboardUpdateResult
	(*RequestOnboardUpdateParams_AdyenParams)(nil), // 10: moego.api.payment.v2.RequestOnboardUpdateParams.AdyenParams
	(v2.ChannelType)(0),                            // 11: moego.models.payment.v2.ChannelType
	(v2.OnboardStatus)(0),                          // 12: moego.models.payment.v2.OnboardStatus
	(*v2.OnboardStep)(nil),                         // 13: moego.models.payment.v2.OnboardStep
	(*v2.AdyenPreConfigDef)(nil),                   // 14: moego.models.payment.v2.AdyenPreConfigDef
	(*v2.ChannelAccount)(nil),                      // 15: moego.models.payment.v2.ChannelAccount
	(*v2.OnboardVerification)(nil),                 // 16: moego.models.payment.v2.OnboardVerification
	(*v2.BankAccount)(nil),                         // 17: moego.models.payment.v2.BankAccount
	(*v2.AdyenBusinessConfig)(nil),                 // 18: moego.models.payment.v2.AdyenBusinessConfig
}
var file_moego_api_payment_v2_payment_onboard_api_proto_depIdxs = []int32{
	11, // 0: moego.api.payment.v2.GetOnboardParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	12, // 1: moego.api.payment.v2.GetOnboardResult.status:type_name -> moego.models.payment.v2.OnboardStatus
	13, // 2: moego.api.payment.v2.GetOnboardResult.steps:type_name -> moego.models.payment.v2.OnboardStep
	11, // 3: moego.api.payment.v2.ProceedOnboardParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	14, // 4: moego.api.payment.v2.ProceedOnboardParams.adyen_pre_config:type_name -> moego.models.payment.v2.AdyenPreConfigDef
	11, // 5: moego.api.payment.v2.ConfirmOnboardParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	11, // 6: moego.api.payment.v2.GetChannelAccountDetailParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	15, // 7: moego.api.payment.v2.GetChannelAccountDetailResult.channel_account:type_name -> moego.models.payment.v2.ChannelAccount
	16, // 8: moego.api.payment.v2.GetChannelAccountDetailResult.verifications:type_name -> moego.models.payment.v2.OnboardVerification
	17, // 9: moego.api.payment.v2.GetChannelAccountDetailResult.bank_accounts:type_name -> moego.models.payment.v2.BankAccount
	11, // 10: moego.api.payment.v2.RequestOnboardUpdateParams.channel_type:type_name -> moego.models.payment.v2.ChannelType
	10, // 11: moego.api.payment.v2.RequestOnboardUpdateParams.adyen:type_name -> moego.api.payment.v2.RequestOnboardUpdateParams.AdyenParams
	18, // 12: moego.api.payment.v2.RequestOnboardUpdateParams.AdyenParams.business_config:type_name -> moego.models.payment.v2.AdyenBusinessConfig
	0,  // 13: moego.api.payment.v2.PaymentOnboardService.GetOnboard:input_type -> moego.api.payment.v2.GetOnboardParams
	2,  // 14: moego.api.payment.v2.PaymentOnboardService.ProceedOnboard:input_type -> moego.api.payment.v2.ProceedOnboardParams
	4,  // 15: moego.api.payment.v2.PaymentOnboardService.ConfirmOnboard:input_type -> moego.api.payment.v2.ConfirmOnboardParams
	6,  // 16: moego.api.payment.v2.PaymentOnboardService.GetChannelAccountDetail:input_type -> moego.api.payment.v2.GetChannelAccountDetailParams
	8,  // 17: moego.api.payment.v2.PaymentOnboardService.RequestOnboardUpdate:input_type -> moego.api.payment.v2.RequestOnboardUpdateParams
	1,  // 18: moego.api.payment.v2.PaymentOnboardService.GetOnboard:output_type -> moego.api.payment.v2.GetOnboardResult
	3,  // 19: moego.api.payment.v2.PaymentOnboardService.ProceedOnboard:output_type -> moego.api.payment.v2.ProceedOnboardResult
	5,  // 20: moego.api.payment.v2.PaymentOnboardService.ConfirmOnboard:output_type -> moego.api.payment.v2.ConfirmOnboardResult
	7,  // 21: moego.api.payment.v2.PaymentOnboardService.GetChannelAccountDetail:output_type -> moego.api.payment.v2.GetChannelAccountDetailResult
	9,  // 22: moego.api.payment.v2.PaymentOnboardService.RequestOnboardUpdate:output_type -> moego.api.payment.v2.RequestOnboardUpdateResult
	18, // [18:23] is the sub-list for method output_type
	13, // [13:18] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_api_payment_v2_payment_onboard_api_proto_init() }
func file_moego_api_payment_v2_payment_onboard_api_proto_init() {
	if File_moego_api_payment_v2_payment_onboard_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProceedOnboardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProceedOnboardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmOnboardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmOnboardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAccountDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAccountDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestOnboardUpdateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestOnboardUpdateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestOnboardUpdateParams_AdyenParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ProceedOnboardParams_AdyenPreConfig)(nil),
	}
	file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*RequestOnboardUpdateParams_Adyen)(nil),
	}
	file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_payment_v2_payment_onboard_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_payment_v2_payment_onboard_api_proto_goTypes,
		DependencyIndexes: file_moego_api_payment_v2_payment_onboard_api_proto_depIdxs,
		MessageInfos:      file_moego_api_payment_v2_payment_onboard_api_proto_msgTypes,
	}.Build()
	File_moego_api_payment_v2_payment_onboard_api_proto = out.File
	file_moego_api_payment_v2_payment_onboard_api_proto_rawDesc = nil
	file_moego_api_payment_v2_payment_onboard_api_proto_goTypes = nil
	file_moego_api_payment_v2_payment_onboard_api_proto_depIdxs = nil
}
