// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v2/payout_models.proto

package paymentpb

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PaymentStatus 支付状态
type PayoutModel_PayoutStatus int32

const (
	// Unspecified
	PayoutModel_STATUS_UNSPECIFIED PayoutModel_PayoutStatus = 0
	// Created，这是初始化状态
	PayoutModel_CREATED PayoutModel_PayoutStatus = 1
	// Submitted，已向支付渠道提交请求,如stripe的in_transit
	PayoutModel_SUBMITTED PayoutModel_PayoutStatus = 2
	// PAID，支付成功，不见得是终态，按照渠道的说法，也可能从paid到failed :https://docs.stripe.com/api/payouts/object
	PayoutModel_PAID PayoutModel_PayoutStatus = 3
	// Cancelled，被取消或者reverse
	PayoutModel_CANCELLED PayoutModel_PayoutStatus = 4
	// Failed，支付失败，这是最终状态
	PayoutModel_FAILED PayoutModel_PayoutStatus = 5
)

// Enum value maps for PayoutModel_PayoutStatus.
var (
	PayoutModel_PayoutStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "SUBMITTED",
		3: "PAID",
		4: "CANCELLED",
		5: "FAILED",
	}
	PayoutModel_PayoutStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"CREATED":            1,
		"SUBMITTED":          2,
		"PAID":               3,
		"CANCELLED":          4,
		"FAILED":             5,
	}
)

func (x PayoutModel_PayoutStatus) Enum() *PayoutModel_PayoutStatus {
	p := new(PayoutModel_PayoutStatus)
	*p = x
	return p
}

func (x PayoutModel_PayoutStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayoutModel_PayoutStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payout_models_proto_enumTypes[0].Descriptor()
}

func (PayoutModel_PayoutStatus) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payout_models_proto_enumTypes[0]
}

func (x PayoutModel_PayoutStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PayoutModel_PayoutStatus.Descriptor instead.
func (PayoutModel_PayoutStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payout_models_proto_rawDescGZIP(), []int{0, 0}
}

// method mode
type PayoutModel_MethodMode int32

const (
	// Unspecified
	PayoutModel_MODE_UNSPECIFIED PayoutModel_MethodMode = 0
	// 标准的payout
	PayoutModel_STANDARD PayoutModel_MethodMode = 1
	// Instant Payout
	PayoutModel_INSTANT PayoutModel_MethodMode = 2
	// Next Day Payout
	PayoutModel_NEXT_DAY_PAYOUT PayoutModel_MethodMode = 3
)

// Enum value maps for PayoutModel_MethodMode.
var (
	PayoutModel_MethodMode_name = map[int32]string{
		0: "MODE_UNSPECIFIED",
		1: "STANDARD",
		2: "INSTANT",
		3: "NEXT_DAY_PAYOUT",
	}
	PayoutModel_MethodMode_value = map[string]int32{
		"MODE_UNSPECIFIED": 0,
		"STANDARD":         1,
		"INSTANT":          2,
		"NEXT_DAY_PAYOUT":  3,
	}
)

func (x PayoutModel_MethodMode) Enum() *PayoutModel_MethodMode {
	p := new(PayoutModel_MethodMode)
	*p = x
	return p
}

func (x PayoutModel_MethodMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayoutModel_MethodMode) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payout_models_proto_enumTypes[1].Descriptor()
}

func (PayoutModel_MethodMode) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payout_models_proto_enumTypes[1]
}

func (x PayoutModel_MethodMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PayoutModel_MethodMode.Descriptor instead.
func (PayoutModel_MethodMode) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payout_models_proto_rawDescGZIP(), []int{0, 1}
}

// destination type
type PayoutModel_DestinationType int32

const (
	// Unspecified
	PayoutModel_TYPE_UNSPECIFIED PayoutModel_DestinationType = 0
	// bank account
	PayoutModel_TYPE_BANK_ACCOUNT PayoutModel_DestinationType = 1
	// debit card
	PayoutModel_TYPE_DEBIT_CARD PayoutModel_DestinationType = 2
)

// Enum value maps for PayoutModel_DestinationType.
var (
	PayoutModel_DestinationType_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "TYPE_BANK_ACCOUNT",
		2: "TYPE_DEBIT_CARD",
	}
	PayoutModel_DestinationType_value = map[string]int32{
		"TYPE_UNSPECIFIED":  0,
		"TYPE_BANK_ACCOUNT": 1,
		"TYPE_DEBIT_CARD":   2,
	}
)

func (x PayoutModel_DestinationType) Enum() *PayoutModel_DestinationType {
	p := new(PayoutModel_DestinationType)
	*p = x
	return p
}

func (x PayoutModel_DestinationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayoutModel_DestinationType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payout_models_proto_enumTypes[2].Descriptor()
}

func (PayoutModel_DestinationType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payout_models_proto_enumTypes[2]
}

func (x PayoutModel_DestinationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PayoutModel_DestinationType.Descriptor instead.
func (PayoutModel_DestinationType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payout_models_proto_rawDescGZIP(), []int{0, 2}
}

// payout model
type PayoutModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	PayoutId int64 `protobuf:"varint,1,opt,name=payout_id,json=payoutId,proto3" json:"payout_id,omitempty"`
	// amount
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// Date that you can expect the payout to arrive in the bank.
	// This factors in delays to account for weekends or bank holidays.
	ArrivalDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=arrival_date,json=arrivalDate,proto3" json:"arrival_date,omitempty"`
	// Enums: PaymentVendor/Moego, used to be is_automatic
	PayoutTriggerSource string `protobuf:"bytes,4,opt,name=payout_trigger_source,json=payoutTriggerSource,proto3" json:"payout_trigger_source,omitempty"`
	// Time at which the object was created. Measured in seconds since the Unix epoch.
	Created *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created,proto3" json:"created,omitempty"`
	// ID of the bank account or card the payout is sent to.
	Destination string `protobuf:"bytes,6,opt,name=destination,proto3" json:"destination,omitempty"`
	// Can be bank_account or card.
	DestinationType PayoutModel_DestinationType `protobuf:"varint,7,opt,name=destination_type,json=destinationType,proto3,enum=moego.models.payment.v2.PayoutModel_DestinationType" json:"destination_type,omitempty"`
	// Error code that provides a reason for a payout failure, if available.
	FailureCode string `protobuf:"bytes,8,opt,name=failure_code,json=failureCode,proto3" json:"failure_code,omitempty"`
	// Message that provides the reason for a payout failure, if available.
	FailureMessage string `protobuf:"bytes,9,opt,name=failure_message,json=failureMessage,proto3" json:"failure_message,omitempty"`
	// The method used to send this payout, which can be standard or instant.
	// https://docs.stripe.com/payouts/instant-payouts-banks
	Mode PayoutModel_MethodMode `protobuf:"varint,10,opt,name=mode,proto3,enum=moego.models.payment.v2.PayoutModel_MethodMode" json:"mode,omitempty"`
	// Extra information about a payout that displays on the user’s bank statement.
	StatementDescriptor string `protobuf:"bytes,11,opt,name=statement_descriptor,json=statementDescriptor,proto3" json:"statement_descriptor,omitempty"`
	// Current status of the payout
	Status PayoutModel_PayoutStatus `protobuf:"varint,12,opt,name=status,proto3,enum=moego.models.payment.v2.PayoutModel_PayoutStatus" json:"status,omitempty"`
	// entityType of the payout
	EntityType EntityType `protobuf:"varint,13,opt,name=entity_type,json=entityType,proto3,enum=moego.models.payment.v2.EntityType" json:"entity_type,omitempty"`
	// entity id
	EntityId int64 `protobuf:"varint,14,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
}

func (x *PayoutModel) Reset() {
	*x = PayoutModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payout_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayoutModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayoutModel) ProtoMessage() {}

func (x *PayoutModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payout_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayoutModel.ProtoReflect.Descriptor instead.
func (*PayoutModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payout_models_proto_rawDescGZIP(), []int{0}
}

func (x *PayoutModel) GetPayoutId() int64 {
	if x != nil {
		return x.PayoutId
	}
	return 0
}

func (x *PayoutModel) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PayoutModel) GetArrivalDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ArrivalDate
	}
	return nil
}

func (x *PayoutModel) GetPayoutTriggerSource() string {
	if x != nil {
		return x.PayoutTriggerSource
	}
	return ""
}

func (x *PayoutModel) GetCreated() *timestamppb.Timestamp {
	if x != nil {
		return x.Created
	}
	return nil
}

func (x *PayoutModel) GetDestination() string {
	if x != nil {
		return x.Destination
	}
	return ""
}

func (x *PayoutModel) GetDestinationType() PayoutModel_DestinationType {
	if x != nil {
		return x.DestinationType
	}
	return PayoutModel_TYPE_UNSPECIFIED
}

func (x *PayoutModel) GetFailureCode() string {
	if x != nil {
		return x.FailureCode
	}
	return ""
}

func (x *PayoutModel) GetFailureMessage() string {
	if x != nil {
		return x.FailureMessage
	}
	return ""
}

func (x *PayoutModel) GetMode() PayoutModel_MethodMode {
	if x != nil {
		return x.Mode
	}
	return PayoutModel_MODE_UNSPECIFIED
}

func (x *PayoutModel) GetStatementDescriptor() string {
	if x != nil {
		return x.StatementDescriptor
	}
	return ""
}

func (x *PayoutModel) GetStatus() PayoutModel_PayoutStatus {
	if x != nil {
		return x.Status
	}
	return PayoutModel_STATUS_UNSPECIFIED
}

func (x *PayoutModel) GetEntityType() EntityType {
	if x != nil {
		return x.EntityType
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

func (x *PayoutModel) GetEntityId() int64 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

// payout summary
type PayoutSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// gross sale
	GrossSaleAmount *money.Money `protobuf:"bytes,1,opt,name=gross_sale_amount,json=grossSaleAmount,proto3" json:"gross_sale_amount,omitempty"`
	// discount
	DiscountAmount *money.Money `protobuf:"bytes,2,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	// tax
	TaxAmount *money.Money `protobuf:"bytes,3,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	// tips
	TipsAmount *money.Money `protobuf:"bytes,4,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// refunded
	RefundedAmount *money.Money `protobuf:"bytes,5,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// application_fee
	ApplicationFeeAmount *money.Money `protobuf:"bytes,6,opt,name=application_fee_amount,json=applicationFeeAmount,proto3" json:"application_fee_amount,omitempty"`
}

func (x *PayoutSummary) Reset() {
	*x = PayoutSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_payout_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayoutSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayoutSummary) ProtoMessage() {}

func (x *PayoutSummary) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_payout_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayoutSummary.ProtoReflect.Descriptor instead.
func (*PayoutSummary) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payout_models_proto_rawDescGZIP(), []int{1}
}

func (x *PayoutSummary) GetGrossSaleAmount() *money.Money {
	if x != nil {
		return x.GrossSaleAmount
	}
	return nil
}

func (x *PayoutSummary) GetDiscountAmount() *money.Money {
	if x != nil {
		return x.DiscountAmount
	}
	return nil
}

func (x *PayoutSummary) GetTaxAmount() *money.Money {
	if x != nil {
		return x.TaxAmount
	}
	return nil
}

func (x *PayoutSummary) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *PayoutSummary) GetRefundedAmount() *money.Money {
	if x != nil {
		return x.RefundedAmount
	}
	return nil
}

func (x *PayoutSummary) GetApplicationFeeAmount() *money.Money {
	if x != nil {
		return x.ApplicationFeeAmount
	}
	return nil
}

var File_moego_models_payment_v2_payout_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v2_payout_models_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x86, 0x08, 0x0a,
	0x0b, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x5f, 0x0a, 0x10, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x43, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x12, 0x31, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x12, 0x49, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x44, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x0c, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55, 0x42, 0x4d,
	0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x49, 0x44, 0x10,
	0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x22, 0x52, 0x0a, 0x0a,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f,
	0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x4e,
	0x45, 0x58, 0x54, 0x5f, 0x44, 0x41, 0x59, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0x03,
	0x22, 0x53, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01,
	0x12, 0x13, 0x0a, 0x0f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x10, 0x02, 0x22, 0xfb, 0x02, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x3e, 0x0a, 0x11, 0x67, 0x72, 0x6f, 0x73, 0x73,
	0x5f, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x67, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x61, 0x6c,
	0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x74, 0x61,
	0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x16, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v2_payout_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v2_payout_models_proto_rawDescData = file_moego_models_payment_v2_payout_models_proto_rawDesc
)

func file_moego_models_payment_v2_payout_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v2_payout_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v2_payout_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v2_payout_models_proto_rawDescData)
	})
	return file_moego_models_payment_v2_payout_models_proto_rawDescData
}

var file_moego_models_payment_v2_payout_models_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_payment_v2_payout_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_payment_v2_payout_models_proto_goTypes = []interface{}{
	(PayoutModel_PayoutStatus)(0),    // 0: moego.models.payment.v2.PayoutModel.PayoutStatus
	(PayoutModel_MethodMode)(0),      // 1: moego.models.payment.v2.PayoutModel.MethodMode
	(PayoutModel_DestinationType)(0), // 2: moego.models.payment.v2.PayoutModel.DestinationType
	(*PayoutModel)(nil),              // 3: moego.models.payment.v2.PayoutModel
	(*PayoutSummary)(nil),            // 4: moego.models.payment.v2.PayoutSummary
	(*money.Money)(nil),              // 5: google.type.Money
	(*timestamppb.Timestamp)(nil),    // 6: google.protobuf.Timestamp
	(EntityType)(0),                  // 7: moego.models.payment.v2.EntityType
}
var file_moego_models_payment_v2_payout_models_proto_depIdxs = []int32{
	5,  // 0: moego.models.payment.v2.PayoutModel.amount:type_name -> google.type.Money
	6,  // 1: moego.models.payment.v2.PayoutModel.arrival_date:type_name -> google.protobuf.Timestamp
	6,  // 2: moego.models.payment.v2.PayoutModel.created:type_name -> google.protobuf.Timestamp
	2,  // 3: moego.models.payment.v2.PayoutModel.destination_type:type_name -> moego.models.payment.v2.PayoutModel.DestinationType
	1,  // 4: moego.models.payment.v2.PayoutModel.mode:type_name -> moego.models.payment.v2.PayoutModel.MethodMode
	0,  // 5: moego.models.payment.v2.PayoutModel.status:type_name -> moego.models.payment.v2.PayoutModel.PayoutStatus
	7,  // 6: moego.models.payment.v2.PayoutModel.entity_type:type_name -> moego.models.payment.v2.EntityType
	5,  // 7: moego.models.payment.v2.PayoutSummary.gross_sale_amount:type_name -> google.type.Money
	5,  // 8: moego.models.payment.v2.PayoutSummary.discount_amount:type_name -> google.type.Money
	5,  // 9: moego.models.payment.v2.PayoutSummary.tax_amount:type_name -> google.type.Money
	5,  // 10: moego.models.payment.v2.PayoutSummary.tips_amount:type_name -> google.type.Money
	5,  // 11: moego.models.payment.v2.PayoutSummary.refunded_amount:type_name -> google.type.Money
	5,  // 12: moego.models.payment.v2.PayoutSummary.application_fee_amount:type_name -> google.type.Money
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v2_payout_models_proto_init() }
func file_moego_models_payment_v2_payout_models_proto_init() {
	if File_moego_models_payment_v2_payout_models_proto != nil {
		return
	}
	file_moego_models_payment_v2_common_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v2_payout_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayoutModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_payout_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayoutSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v2_payout_models_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v2_payout_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v2_payout_models_proto_depIdxs,
		EnumInfos:         file_moego_models_payment_v2_payout_models_proto_enumTypes,
		MessageInfos:      file_moego_models_payment_v2_payout_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v2_payout_models_proto = out.File
	file_moego_models_payment_v2_payout_models_proto_rawDesc = nil
	file_moego_models_payment_v2_payout_models_proto_goTypes = nil
	file_moego_models_payment_v2_payout_models_proto_depIdxs = nil
}
