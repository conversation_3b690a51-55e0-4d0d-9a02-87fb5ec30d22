// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v2/payment_enums.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PaymentVersion 表示的是支付版本
type PaymentVersion int32

const (
	// Unspecified
	PaymentVersion_PAYMENT_VERSION_UNSPECIFIED PaymentVersion = 0
	// V1
	PaymentVersion_V1 PaymentVersion = 1
	// V2
	PaymentVersion_V2 PaymentVersion = 2
)

// Enum value maps for PaymentVersion.
var (
	PaymentVersion_name = map[int32]string{
		0: "PAYMENT_VERSION_UNSPECIFIED",
		1: "V1",
		2: "V2",
	}
	PaymentVersion_value = map[string]int32{
		"PAYMENT_VERSION_UNSPECIFIED": 0,
		"V1":                          1,
		"V2":                          2,
	}
)

func (x PaymentVersion) Enum() *PaymentVersion {
	p := new(PaymentVersion)
	*p = x
	return p
}

func (x PaymentVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_enums_proto_enumTypes[0].Descriptor()
}

func (PaymentVersion) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_enums_proto_enumTypes[0]
}

func (x PaymentVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentVersion.Descriptor instead.
func (PaymentVersion) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_enums_proto_rawDescGZIP(), []int{0}
}

// ExternalType 表示的是上层业务系统的类型
type ExternalType int32

const (
	// Unspecified
	ExternalType_EXTERNAL_TYPE_UNSPECIFIED ExternalType = 0
	// Order Payment 对应order的order payment实体
	ExternalType_ORDER_PAYMENT ExternalType = 1
	// Refund Order Payment 对应 order 的 refund order payment 实体
	ExternalType_REFUND_ORDER_PAYMENT ExternalType = 2
	// Order 对应 order 域的 order 实体
	ExternalType_ORDER ExternalType = 3
)

// Enum value maps for ExternalType.
var (
	ExternalType_name = map[int32]string{
		0: "EXTERNAL_TYPE_UNSPECIFIED",
		1: "ORDER_PAYMENT",
		2: "REFUND_ORDER_PAYMENT",
		3: "ORDER",
	}
	ExternalType_value = map[string]int32{
		"EXTERNAL_TYPE_UNSPECIFIED": 0,
		"ORDER_PAYMENT":             1,
		"REFUND_ORDER_PAYMENT":      2,
		"ORDER":                     3,
	}
)

func (x ExternalType) Enum() *ExternalType {
	p := new(ExternalType)
	*p = x
	return p
}

func (x ExternalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExternalType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_enums_proto_enumTypes[1].Descriptor()
}

func (ExternalType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_enums_proto_enumTypes[1]
}

func (x ExternalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExternalType.Descriptor instead.
func (ExternalType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_enums_proto_rawDescGZIP(), []int{1}
}

// Reader 类型
type ReaderType int32

const (
	// Unspecified
	ReaderType_READER_TYPE_UNSPECIFIED ReaderType = 0
	// Smart reader
	ReaderType_SMART_READER ReaderType = 1
	// BT reader
	ReaderType_BLUETOOTH_READER ReaderType = 2
	// Tap-to-pay reader
	ReaderType_TAP_TO_PAY_READER ReaderType = 3
)

// Enum value maps for ReaderType.
var (
	ReaderType_name = map[int32]string{
		0: "READER_TYPE_UNSPECIFIED",
		1: "SMART_READER",
		2: "BLUETOOTH_READER",
		3: "TAP_TO_PAY_READER",
	}
	ReaderType_value = map[string]int32{
		"READER_TYPE_UNSPECIFIED": 0,
		"SMART_READER":            1,
		"BLUETOOTH_READER":        2,
		"TAP_TO_PAY_READER":       3,
	}
)

func (x ReaderType) Enum() *ReaderType {
	p := new(ReaderType)
	*p = x
	return p
}

func (x ReaderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReaderType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_enums_proto_enumTypes[2].Descriptor()
}

func (ReaderType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_enums_proto_enumTypes[2]
}

func (x ReaderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReaderType.Descriptor instead.
func (ReaderType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_enums_proto_rawDescGZIP(), []int{2}
}

// processing fee paid by 类型
type ProcessingFeePaidBy int32

const (
	// Unspecified
	ProcessingFeePaidBy_PROCESSING_FEE_PAID_BY_UNSPECIFIED ProcessingFeePaidBy = 0
	// paid by business
	ProcessingFeePaidBy_PAID_BY_BUSINESS ProcessingFeePaidBy = 1
	// paid by client
	ProcessingFeePaidBy_PAID_BY_CLIENT ProcessingFeePaidBy = 2
)

// Enum value maps for ProcessingFeePaidBy.
var (
	ProcessingFeePaidBy_name = map[int32]string{
		0: "PROCESSING_FEE_PAID_BY_UNSPECIFIED",
		1: "PAID_BY_BUSINESS",
		2: "PAID_BY_CLIENT",
	}
	ProcessingFeePaidBy_value = map[string]int32{
		"PROCESSING_FEE_PAID_BY_UNSPECIFIED": 0,
		"PAID_BY_BUSINESS":                   1,
		"PAID_BY_CLIENT":                     2,
	}
)

func (x ProcessingFeePaidBy) Enum() *ProcessingFeePaidBy {
	p := new(ProcessingFeePaidBy)
	*p = x
	return p
}

func (x ProcessingFeePaidBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessingFeePaidBy) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_enums_proto_enumTypes[3].Descriptor()
}

func (ProcessingFeePaidBy) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_enums_proto_enumTypes[3]
}

func (x ProcessingFeePaidBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessingFeePaidBy.Descriptor instead.
func (ProcessingFeePaidBy) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_enums_proto_rawDescGZIP(), []int{3}
}

// funding source 资金来源
type FundingSource int32

const (
	// Unspecified
	FundingSource_FUNDING_SOURCE_UNSPECIFIED FundingSource = 0
	// credit
	FundingSource_CREDIT FundingSource = 1
	// debit
	FundingSource_DEBIT FundingSource = 2
)

// Enum value maps for FundingSource.
var (
	FundingSource_name = map[int32]string{
		0: "FUNDING_SOURCE_UNSPECIFIED",
		1: "CREDIT",
		2: "DEBIT",
	}
	FundingSource_value = map[string]int32{
		"FUNDING_SOURCE_UNSPECIFIED": 0,
		"CREDIT":                     1,
		"DEBIT":                      2,
	}
)

func (x FundingSource) Enum() *FundingSource {
	p := new(FundingSource)
	*p = x
	return p
}

func (x FundingSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FundingSource) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v2_payment_enums_proto_enumTypes[4].Descriptor()
}

func (FundingSource) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v2_payment_enums_proto_enumTypes[4]
}

func (x FundingSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FundingSource.Descriptor instead.
func (FundingSource) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_payment_enums_proto_rawDescGZIP(), []int{4}
}

var File_moego_models_payment_v2_payment_enums_proto protoreflect.FileDescriptor

var file_moego_models_payment_v2_payment_enums_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2a, 0x41, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x56, 0x31, 0x10,
	0x01, 0x12, 0x06, 0x0a, 0x02, 0x56, 0x32, 0x10, 0x02, 0x2a, 0x65, 0x0a, 0x0c, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x58, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x03,
	0x2a, 0x68, 0x0a, 0x0a, 0x52, 0x65, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x17, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53,
	0x4d, 0x41, 0x52, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x42, 0x4c, 0x55, 0x45, 0x54, 0x4f, 0x4f, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x45,
	0x52, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x41, 0x50, 0x5f, 0x54, 0x4f, 0x5f, 0x50, 0x41,
	0x59, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x03, 0x2a, 0x67, 0x0a, 0x13, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x50, 0x61, 0x69, 0x64, 0x42,
	0x79, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x45, 0x45, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x41, 0x49,
	0x44, 0x5f, 0x42, 0x59, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12,
	0x12, 0x0a, 0x0e, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x10, 0x02, 0x2a, 0x46, 0x0a, 0x0d, 0x46, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x01,
	0x12, 0x09, 0x0a, 0x05, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x02, 0x42, 0x7b, 0x0a, 0x1f, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01,
	0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v2_payment_enums_proto_rawDescOnce sync.Once
	file_moego_models_payment_v2_payment_enums_proto_rawDescData = file_moego_models_payment_v2_payment_enums_proto_rawDesc
)

func file_moego_models_payment_v2_payment_enums_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v2_payment_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v2_payment_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v2_payment_enums_proto_rawDescData)
	})
	return file_moego_models_payment_v2_payment_enums_proto_rawDescData
}

var file_moego_models_payment_v2_payment_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_moego_models_payment_v2_payment_enums_proto_goTypes = []interface{}{
	(PaymentVersion)(0),      // 0: moego.models.payment.v2.PaymentVersion
	(ExternalType)(0),        // 1: moego.models.payment.v2.ExternalType
	(ReaderType)(0),          // 2: moego.models.payment.v2.ReaderType
	(ProcessingFeePaidBy)(0), // 3: moego.models.payment.v2.ProcessingFeePaidBy
	(FundingSource)(0),       // 4: moego.models.payment.v2.FundingSource
}
var file_moego_models_payment_v2_payment_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v2_payment_enums_proto_init() }
func file_moego_models_payment_v2_payment_enums_proto_init() {
	if File_moego_models_payment_v2_payment_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v2_payment_enums_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v2_payment_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v2_payment_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_payment_v2_payment_enums_proto_enumTypes,
	}.Build()
	File_moego_models_payment_v2_payment_enums_proto = out.File
	file_moego_models_payment_v2_payment_enums_proto_rawDesc = nil
	file_moego_models_payment_v2_payment_enums_proto_goTypes = nil
	file_moego_models_payment_v2_payment_enums_proto_depIdxs = nil
}
