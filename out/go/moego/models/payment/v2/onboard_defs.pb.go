// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v2/onboard_defs.proto

package paymentpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Def for Adyen pre-configuration
type AdyenPreConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Phone number of this company.
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// One of the legal entity type. Trust is not supported.
	//
	// Types that are assignable to LegalEntity:
	//
	//	*AdyenPreConfigDef_Individual_
	//	*AdyenPreConfigDef_Organization_
	//	*AdyenPreConfigDef_SoleProprietorship_
	LegalEntity isAdyenPreConfigDef_LegalEntity `protobuf_oneof:"legal_entity"`
	// Additional configuration for business, keyed by business ID. These configurations override the business profile.
	BusinessConfig map[int64]*AdyenBusinessConfig `protobuf:"bytes,5,rep,name=business_config,json=businessConfig,proto3" json:"business_config,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *AdyenPreConfigDef) Reset() {
	*x = AdyenPreConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdyenPreConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdyenPreConfigDef) ProtoMessage() {}

func (x *AdyenPreConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdyenPreConfigDef.ProtoReflect.Descriptor instead.
func (*AdyenPreConfigDef) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP(), []int{0}
}

func (x *AdyenPreConfigDef) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (m *AdyenPreConfigDef) GetLegalEntity() isAdyenPreConfigDef_LegalEntity {
	if m != nil {
		return m.LegalEntity
	}
	return nil
}

func (x *AdyenPreConfigDef) GetIndividual() *AdyenPreConfigDef_Individual {
	if x, ok := x.GetLegalEntity().(*AdyenPreConfigDef_Individual_); ok {
		return x.Individual
	}
	return nil
}

func (x *AdyenPreConfigDef) GetOrganization() *AdyenPreConfigDef_Organization {
	if x, ok := x.GetLegalEntity().(*AdyenPreConfigDef_Organization_); ok {
		return x.Organization
	}
	return nil
}

func (x *AdyenPreConfigDef) GetSoleProprietorship() *AdyenPreConfigDef_SoleProprietorship {
	if x, ok := x.GetLegalEntity().(*AdyenPreConfigDef_SoleProprietorship_); ok {
		return x.SoleProprietorship
	}
	return nil
}

func (x *AdyenPreConfigDef) GetBusinessConfig() map[int64]*AdyenBusinessConfig {
	if x != nil {
		return x.BusinessConfig
	}
	return nil
}

type isAdyenPreConfigDef_LegalEntity interface {
	isAdyenPreConfigDef_LegalEntity()
}

type AdyenPreConfigDef_Individual_ struct {
	// Individual legal entity.
	Individual *AdyenPreConfigDef_Individual `protobuf:"bytes,2,opt,name=individual,proto3,oneof"`
}

type AdyenPreConfigDef_Organization_ struct {
	// Organization legal entity.
	Organization *AdyenPreConfigDef_Organization `protobuf:"bytes,3,opt,name=organization,proto3,oneof"`
}

type AdyenPreConfigDef_SoleProprietorship_ struct {
	// Sole proprietorship legal entity.
	SoleProprietorship *AdyenPreConfigDef_SoleProprietorship `protobuf:"bytes,4,opt,name=sole_proprietorship,json=soleProprietorship,proto3,oneof"`
}

func (*AdyenPreConfigDef_Individual_) isAdyenPreConfigDef_LegalEntity() {}

func (*AdyenPreConfigDef_Organization_) isAdyenPreConfigDef_LegalEntity() {}

func (*AdyenPreConfigDef_SoleProprietorship_) isAdyenPreConfigDef_LegalEntity() {}

// Additional configuration for business.
type AdyenBusinessConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Store address.
	StoreAddress *v1.AddressDef `protobuf:"bytes,1,opt,name=store_address,json=storeAddress,proto3,oneof" json:"store_address,omitempty"`
	// Store phone number.
	StorePhoneNumber *string `protobuf:"bytes,2,opt,name=store_phone_number,json=storePhoneNumber,proto3,oneof" json:"store_phone_number,omitempty"`
}

func (x *AdyenBusinessConfig) Reset() {
	*x = AdyenBusinessConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdyenBusinessConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdyenBusinessConfig) ProtoMessage() {}

func (x *AdyenBusinessConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdyenBusinessConfig.ProtoReflect.Descriptor instead.
func (*AdyenBusinessConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP(), []int{1}
}

func (x *AdyenBusinessConfig) GetStoreAddress() *v1.AddressDef {
	if x != nil {
		return x.StoreAddress
	}
	return nil
}

func (x *AdyenBusinessConfig) GetStorePhoneNumber() string {
	if x != nil && x.StorePhoneNumber != nil {
		return *x.StorePhoneNumber
	}
	return ""
}

// Invalid fields error reported by Adyen.
type AdyenInvalidFieldsError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Invalid fields.
	InvalidFields []*AdyenInvalidFieldsError_InvalidField `protobuf:"bytes,1,rep,name=invalid_fields,json=invalidFields,proto3" json:"invalid_fields,omitempty"`
	// The type of the entity which causes the error
	EntityType EntityType `protobuf:"varint,2,opt,name=entity_type,json=entityType,proto3,enum=moego.models.payment.v2.EntityType" json:"entity_type,omitempty"`
	// The id of the entity which causes the error
	EntityId int64 `protobuf:"varint,3,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
}

func (x *AdyenInvalidFieldsError) Reset() {
	*x = AdyenInvalidFieldsError{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdyenInvalidFieldsError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdyenInvalidFieldsError) ProtoMessage() {}

func (x *AdyenInvalidFieldsError) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdyenInvalidFieldsError.ProtoReflect.Descriptor instead.
func (*AdyenInvalidFieldsError) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP(), []int{2}
}

func (x *AdyenInvalidFieldsError) GetInvalidFields() []*AdyenInvalidFieldsError_InvalidField {
	if x != nil {
		return x.InvalidFields
	}
	return nil
}

func (x *AdyenInvalidFieldsError) GetEntityType() EntityType {
	if x != nil {
		return x.EntityType
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

func (x *AdyenInvalidFieldsError) GetEntityId() int64 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

// Individual legal entity.
// 注意：Adyen 不允许我们直接 onboard individual，必须通过 sole proprietorship 方式 onboard individual。这里我们放开
// individual 类型，但是商家在 onboard link 里应当选择 sole proprietorship 完成后续步骤。
type AdyenPreConfigDef_Individual struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Given name (first name).
	GivenName string `protobuf:"bytes,1,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// Family name (last name).
	FamilyName string `protobuf:"bytes,2,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// Residential address country alpha2 code.
	ResidentialAddressCountry string `protobuf:"bytes,3,opt,name=residential_address_country,json=residentialAddressCountry,proto3" json:"residential_address_country,omitempty"`
}

func (x *AdyenPreConfigDef_Individual) Reset() {
	*x = AdyenPreConfigDef_Individual{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdyenPreConfigDef_Individual) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdyenPreConfigDef_Individual) ProtoMessage() {}

func (x *AdyenPreConfigDef_Individual) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdyenPreConfigDef_Individual.ProtoReflect.Descriptor instead.
func (*AdyenPreConfigDef_Individual) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP(), []int{0, 0}
}

func (x *AdyenPreConfigDef_Individual) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *AdyenPreConfigDef_Individual) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *AdyenPreConfigDef_Individual) GetResidentialAddressCountry() string {
	if x != nil {
		return x.ResidentialAddressCountry
	}
	return ""
}

// Organization legal entity.
type AdyenPreConfigDef_Organization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Legal name
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: The "legal_name" is reasonable. --)
	LegalName string `protobuf:"bytes,1,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	// Registered address country alpha2 code.
	RegisteredAddressCountry string `protobuf:"bytes,2,opt,name=registered_address_country,json=registeredAddressCountry,proto3" json:"registered_address_country,omitempty"`
}

func (x *AdyenPreConfigDef_Organization) Reset() {
	*x = AdyenPreConfigDef_Organization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdyenPreConfigDef_Organization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdyenPreConfigDef_Organization) ProtoMessage() {}

func (x *AdyenPreConfigDef_Organization) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdyenPreConfigDef_Organization.ProtoReflect.Descriptor instead.
func (*AdyenPreConfigDef_Organization) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP(), []int{0, 1}
}

func (x *AdyenPreConfigDef_Organization) GetLegalName() string {
	if x != nil {
		return x.LegalName
	}
	return ""
}

func (x *AdyenPreConfigDef_Organization) GetRegisteredAddressCountry() string {
	if x != nil {
		return x.RegisteredAddressCountry
	}
	return ""
}

// Sole proprietorship legal entity.
// 根据 Adyen 的要求：Sole Proprietorship 的 main legal entity 也是 individual 类型，本质上是在 individual 上关联了一个
// Sole Proprietorship 类型的 legal entity。因此这里入参调整为和 individual 一样。
type AdyenPreConfigDef_SoleProprietorship struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Given name (first name).
	GivenName string `protobuf:"bytes,1,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// Family name (last name).
	FamilyName string `protobuf:"bytes,2,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// Residential address country alpha2 code.
	ResidentialAddressCountry string `protobuf:"bytes,3,opt,name=residential_address_country,json=residentialAddressCountry,proto3" json:"residential_address_country,omitempty"`
}

func (x *AdyenPreConfigDef_SoleProprietorship) Reset() {
	*x = AdyenPreConfigDef_SoleProprietorship{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdyenPreConfigDef_SoleProprietorship) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdyenPreConfigDef_SoleProprietorship) ProtoMessage() {}

func (x *AdyenPreConfigDef_SoleProprietorship) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdyenPreConfigDef_SoleProprietorship.ProtoReflect.Descriptor instead.
func (*AdyenPreConfigDef_SoleProprietorship) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP(), []int{0, 2}
}

func (x *AdyenPreConfigDef_SoleProprietorship) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *AdyenPreConfigDef_SoleProprietorship) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *AdyenPreConfigDef_SoleProprietorship) GetResidentialAddressCountry() string {
	if x != nil {
		return x.ResidentialAddressCountry
	}
	return ""
}

// Invalid field.
type AdyenInvalidFieldsError_InvalidField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Message for detail.
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	// Field name in a.b.c format.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Invalid value passed to Adyen.
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *AdyenInvalidFieldsError_InvalidField) Reset() {
	*x = AdyenInvalidFieldsError_InvalidField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdyenInvalidFieldsError_InvalidField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdyenInvalidFieldsError_InvalidField) ProtoMessage() {}

func (x *AdyenInvalidFieldsError_InvalidField) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdyenInvalidFieldsError_InvalidField.ProtoReflect.Descriptor instead.
func (*AdyenInvalidFieldsError_InvalidField) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP(), []int{2, 0}
}

func (x *AdyenInvalidFieldsError_InvalidField) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AdyenInvalidFieldsError_InvalidField) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AdyenInvalidFieldsError_InvalidField) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_moego_models_payment_v2_onboard_defs_proto protoreflect.FileDescriptor

var file_moego_models_payment_v2_onboard_defs_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xdd, 0x07, 0x0a, 0x11, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x72, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x0a, 0x69,
	0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50,
	0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x2e, 0x49, 0x6e, 0x64, 0x69,
	0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x64, 0x69, 0x76, 0x69,
	0x64, 0x75, 0x61, 0x6c, 0x12, 0x5d, 0x0a, 0x0c, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0c, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x70, 0x0a, 0x13, 0x73, 0x6f, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x70,
	0x72, 0x69, 0x65, 0x74, 0x6f, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e,
	0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x6f, 0x6c,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x6f, 0x72, 0x73, 0x68, 0x69, 0x70, 0x48,
	0x00, 0x52, 0x12, 0x73, 0x6f, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x6f,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x67, 0x0a, 0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x72,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x8c,
	0x01, 0x0a, 0x0a, 0x49, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a,
	0x1b, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x19, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x1a, 0x6b, 0x0a,
	0x0c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x18, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x1a, 0x94, 0x01, 0x0a, 0x12, 0x53,
	0x6f, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x72, 0x69, 0x65, 0x74, 0x6f, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x3e, 0x0a, 0x1b, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x1a, 0x6f, 0x0a, 0x13, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x22, 0xc5, 0x01, 0x0a, 0x13, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x52, 0x0a, 0x0d, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0c, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x12, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x10, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01,
	0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xb6, 0x02, 0x0a, 0x17, 0x41,
	0x64, 0x79, 0x65, 0x6e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x64, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x0d, 0x69,
	0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x0b,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x1a,
	0x52, 0x0a, 0x0c, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v2_onboard_defs_proto_rawDescOnce sync.Once
	file_moego_models_payment_v2_onboard_defs_proto_rawDescData = file_moego_models_payment_v2_onboard_defs_proto_rawDesc
)

func file_moego_models_payment_v2_onboard_defs_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v2_onboard_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v2_onboard_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v2_onboard_defs_proto_rawDescData)
	})
	return file_moego_models_payment_v2_onboard_defs_proto_rawDescData
}

var file_moego_models_payment_v2_onboard_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_models_payment_v2_onboard_defs_proto_goTypes = []interface{}{
	(*AdyenPreConfigDef)(nil),                    // 0: moego.models.payment.v2.AdyenPreConfigDef
	(*AdyenBusinessConfig)(nil),                  // 1: moego.models.payment.v2.AdyenBusinessConfig
	(*AdyenInvalidFieldsError)(nil),              // 2: moego.models.payment.v2.AdyenInvalidFieldsError
	(*AdyenPreConfigDef_Individual)(nil),         // 3: moego.models.payment.v2.AdyenPreConfigDef.Individual
	(*AdyenPreConfigDef_Organization)(nil),       // 4: moego.models.payment.v2.AdyenPreConfigDef.Organization
	(*AdyenPreConfigDef_SoleProprietorship)(nil), // 5: moego.models.payment.v2.AdyenPreConfigDef.SoleProprietorship
	nil, // 6: moego.models.payment.v2.AdyenPreConfigDef.BusinessConfigEntry
	(*AdyenInvalidFieldsError_InvalidField)(nil), // 7: moego.models.payment.v2.AdyenInvalidFieldsError.InvalidField
	(*v1.AddressDef)(nil),                        // 8: moego.models.organization.v1.AddressDef
	(EntityType)(0),                              // 9: moego.models.payment.v2.EntityType
}
var file_moego_models_payment_v2_onboard_defs_proto_depIdxs = []int32{
	3, // 0: moego.models.payment.v2.AdyenPreConfigDef.individual:type_name -> moego.models.payment.v2.AdyenPreConfigDef.Individual
	4, // 1: moego.models.payment.v2.AdyenPreConfigDef.organization:type_name -> moego.models.payment.v2.AdyenPreConfigDef.Organization
	5, // 2: moego.models.payment.v2.AdyenPreConfigDef.sole_proprietorship:type_name -> moego.models.payment.v2.AdyenPreConfigDef.SoleProprietorship
	6, // 3: moego.models.payment.v2.AdyenPreConfigDef.business_config:type_name -> moego.models.payment.v2.AdyenPreConfigDef.BusinessConfigEntry
	8, // 4: moego.models.payment.v2.AdyenBusinessConfig.store_address:type_name -> moego.models.organization.v1.AddressDef
	7, // 5: moego.models.payment.v2.AdyenInvalidFieldsError.invalid_fields:type_name -> moego.models.payment.v2.AdyenInvalidFieldsError.InvalidField
	9, // 6: moego.models.payment.v2.AdyenInvalidFieldsError.entity_type:type_name -> moego.models.payment.v2.EntityType
	1, // 7: moego.models.payment.v2.AdyenPreConfigDef.BusinessConfigEntry.value:type_name -> moego.models.payment.v2.AdyenBusinessConfig
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v2_onboard_defs_proto_init() }
func file_moego_models_payment_v2_onboard_defs_proto_init() {
	if File_moego_models_payment_v2_onboard_defs_proto != nil {
		return
	}
	file_moego_models_payment_v2_common_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v2_onboard_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdyenPreConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdyenBusinessConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdyenInvalidFieldsError); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdyenPreConfigDef_Individual); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdyenPreConfigDef_Organization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdyenPreConfigDef_SoleProprietorship); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdyenInvalidFieldsError_InvalidField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_payment_v2_onboard_defs_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*AdyenPreConfigDef_Individual_)(nil),
		(*AdyenPreConfigDef_Organization_)(nil),
		(*AdyenPreConfigDef_SoleProprietorship_)(nil),
	}
	file_moego_models_payment_v2_onboard_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v2_onboard_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v2_onboard_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v2_onboard_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v2_onboard_defs_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v2_onboard_defs_proto = out.File
	file_moego_models_payment_v2_onboard_defs_proto_rawDesc = nil
	file_moego_models_payment_v2_onboard_defs_proto_goTypes = nil
	file_moego_models_payment_v2_onboard_defs_proto_depIdxs = nil
}
