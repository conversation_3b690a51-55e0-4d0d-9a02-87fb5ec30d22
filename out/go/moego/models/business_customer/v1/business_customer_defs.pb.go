// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_customer_defs.proto

package businesscustomerpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create def for business customer
type BusinessCustomerCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source, required
	Source BusinessCustomerInfoModel_Source `protobuf:"varint,1,opt,name=source,proto3,enum=moego.models.business_customer.v1.BusinessCustomerInfoModel_Source" json:"source,omitempty"`
	// phone number, required
	// 手机号格式校验放在业务代码里处理, 这里只检查长度
	// 对于 DM 进来的数据, 允许手机号为空
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// email, default is empty
	// 邮箱格式校验放在业务代码里处理, 这里只检查长度
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// avatar path, default is empty
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// first name, default is empty
	// 业务上必填, 但是也兼容不填的情况, 为以后业务变更做准备
	FirstName string `protobuf:"bytes,5,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name, default is empty
	// 业务上必填, 但是也兼容不填的情况, 为以后业务变更做准备
	LastName string `protobuf:"bytes,6,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// client color, default is empty
	ClientColor *string `protobuf:"bytes,7,opt,name=client_color,json=clientColor,proto3,oneof" json:"client_color,omitempty"`
	// preferred business id
	// 目前业务上是必填的, 但是也兼容 preferred business 不填的情况, 为以后业务变更做准备
	// 不填默认为 0 (没有 preferred business)
	PreferredBusinessId *int64 `protobuf:"varint,8,opt,name=preferred_business_id,json=preferredBusinessId,proto3,oneof" json:"preferred_business_id,omitempty"`
	// account id
	// 是否绑定一个账号, B 端场景通常不传, C 端 (主要是 Branded App) 创建时会传
	// 不填默认为 0 (没有绑定账号)
	AccountId *int64 `protobuf:"varint,9,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// referral source id
	// 不填默认为 0 (没有 referral source id)
	ReferralSourceId *int64 `protobuf:"varint,10,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// customer tag ids
	TagIds []int64 `protobuf:"varint,11,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// external id
	// 默认不填, 只有 DM 会在创建时传入
	ExternalId *string `protobuf:"bytes,12,opt,name=external_id,json=externalId,proto3,oneof" json:"external_id,omitempty"`
	// created at
	// 默认不填, 只有 DM 会在创建时传入
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
	// updated at
	// 默认不填, 只有 DM 会在创建时传入
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
	// birthday
	Birthday *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// referral source desc 这个字段和 moe_customer_source 表中的name字段是不同含义，只针对 app 端 使用，web端没有写入场景
	ReferralSourceDesc *string `protobuf:"bytes,16,opt,name=referral_source_desc,json=referralSourceDesc,proto3,oneof" json:"referral_source_desc,omitempty"`
}

func (x *BusinessCustomerCreateDef) Reset() {
	*x = BusinessCustomerCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerCreateDef) ProtoMessage() {}

func (x *BusinessCustomerCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerCreateDef.ProtoReflect.Descriptor instead.
func (*BusinessCustomerCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessCustomerCreateDef) GetSource() BusinessCustomerInfoModel_Source {
	if x != nil {
		return x.Source
	}
	return BusinessCustomerInfoModel_SOURCE_UNSPECIFIED
}

func (x *BusinessCustomerCreateDef) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BusinessCustomerCreateDef) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BusinessCustomerCreateDef) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessCustomerCreateDef) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerCreateDef) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BusinessCustomerCreateDef) GetClientColor() string {
	if x != nil && x.ClientColor != nil {
		return *x.ClientColor
	}
	return ""
}

func (x *BusinessCustomerCreateDef) GetPreferredBusinessId() int64 {
	if x != nil && x.PreferredBusinessId != nil {
		return *x.PreferredBusinessId
	}
	return 0
}

func (x *BusinessCustomerCreateDef) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *BusinessCustomerCreateDef) GetReferralSourceId() int64 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *BusinessCustomerCreateDef) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *BusinessCustomerCreateDef) GetExternalId() string {
	if x != nil && x.ExternalId != nil {
		return *x.ExternalId
	}
	return ""
}

func (x *BusinessCustomerCreateDef) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BusinessCustomerCreateDef) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *BusinessCustomerCreateDef) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *BusinessCustomerCreateDef) GetReferralSourceDesc() string {
	if x != nil && x.ReferralSourceDesc != nil {
		return *x.ReferralSourceDesc
	}
	return ""
}

// update def for business customer with additional info
type BusinessCustomerWithAdditionalInfoCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer
	Customer *BusinessCustomerCreateDef `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// communication preference, optional
	CommunicationPreference *BusinessCustomerCommunicationPreferenceUpdateDef `protobuf:"bytes,2,opt,name=communication_preference,json=communicationPreference,proto3,oneof" json:"communication_preference,omitempty"`
	// appointment preference, optional
	AppointmentPreference *BusinessCustomerAppointmentPreferenceUpdateDef `protobuf:"bytes,3,opt,name=appointment_preference,json=appointmentPreference,proto3,oneof" json:"appointment_preference,omitempty"`
	// payment preference, optional
	PaymentPreference *BusinessCustomerPaymentPreferenceUpdateDef `protobuf:"bytes,4,opt,name=payment_preference,json=paymentPreference,proto3,oneof" json:"payment_preference,omitempty"`
	// primary address, optional
	// `is_primary` field is ignored and will be set to true.
	PrimaryAddress *BusinessCustomerAddressCreateDef `protobuf:"bytes,5,opt,name=primary_address,json=primaryAddress,proto3,oneof" json:"primary_address,omitempty"`
	// additional addresses, empty means no additional addresses
	// `is_primary` field is ignored and will be set to false.
	// if `primary_address` is not set, the first address in this list will be set as primary address.
	AdditionalAddresses []*BusinessCustomerAddressCreateDef `protobuf:"bytes,6,rep,name=additional_addresses,json=additionalAddresses,proto3" json:"additional_addresses,omitempty"`
	// customer notes, empty means no notes
	Notes []*BusinessCustomerNoteCreateDef `protobuf:"bytes,7,rep,name=notes,proto3" json:"notes,omitempty"`
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) Reset() {
	*x = BusinessCustomerWithAdditionalInfoCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerWithAdditionalInfoCreateDef) ProtoMessage() {}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerWithAdditionalInfoCreateDef.ProtoReflect.Descriptor instead.
func (*BusinessCustomerWithAdditionalInfoCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) GetCustomer() *BusinessCustomerCreateDef {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) GetCommunicationPreference() *BusinessCustomerCommunicationPreferenceUpdateDef {
	if x != nil {
		return x.CommunicationPreference
	}
	return nil
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) GetAppointmentPreference() *BusinessCustomerAppointmentPreferenceUpdateDef {
	if x != nil {
		return x.AppointmentPreference
	}
	return nil
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) GetPaymentPreference() *BusinessCustomerPaymentPreferenceUpdateDef {
	if x != nil {
		return x.PaymentPreference
	}
	return nil
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) GetPrimaryAddress() *BusinessCustomerAddressCreateDef {
	if x != nil {
		return x.PrimaryAddress
	}
	return nil
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) GetAdditionalAddresses() []*BusinessCustomerAddressCreateDef {
	if x != nil {
		return x.AdditionalAddresses
	}
	return nil
}

func (x *BusinessCustomerWithAdditionalInfoCreateDef) GetNotes() []*BusinessCustomerNoteCreateDef {
	if x != nil {
		return x.Notes
	}
	return nil
}

// update def for business customer
type BusinessCustomerUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// avatar path, optional
	// if set to empty, avatar path will be cleared
	AvatarPath *string `protobuf:"bytes,1,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// first name, optional
	// if set to empty, first name will be cleared
	FirstName *string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name, optional
	// if set to empty, last name will be cleared
	LastName *string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// customer tags, optional
	// if this message is not set, customer tags of the customer will not be updated
	// if this message is set but the ids field is empty, customer tags of the customer will be cleared
	CustomerTags *BusinessCustomerUpdateDef_CustomerTagList `protobuf:"bytes,4,opt,name=customer_tags,json=customerTags,proto3,oneof" json:"customer_tags,omitempty"`
	// email, optional
	Email *string `protobuf:"bytes,5,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// birthday
	Birthday *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// address, optional
	Address *BusinessCustomerAddressUpdateDef `protobuf:"bytes,7,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// preferred groomer id, optional
	PreferredGroomerId *int64 `protobuf:"varint,8,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3,oneof" json:"preferred_groomer_id,omitempty"`
	// Emergency contact, optional
	EmergencyContact *BusinessCustomerUpdateDef_EmergencyContact `protobuf:"bytes,9,opt,name=emergency_contact,json=emergencyContact,proto3,oneof" json:"emergency_contact,omitempty"`
	// Authorized user to pickup the pet, optional
	PickupContact *BusinessCustomerUpdateDef_PickupContact `protobuf:"bytes,10,opt,name=pickup_contact,json=pickupContact,proto3,oneof" json:"pickup_contact,omitempty"`
	// phone number
	PhoneNumber *string `protobuf:"bytes,11,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// referral source id
	ReferralSourceId *int32 `protobuf:"varint,12,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// preferred frequency day
	PreferredFrequencyDay *int32 `protobuf:"varint,13,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3,oneof" json:"preferred_frequency_day,omitempty"`
	// preferred frequency type
	// deprecated by Freeman since 2025/7/22, 这个枚举定义的 number 和之前的数据没对上，use preferred_frequency_type_v2 instead
	//
	// Deprecated: Do not use.
	PreferredFrequencyType *v1.PreferredFrequencyType `protobuf:"varint,14,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3,enum=moego.models.customer.v1.PreferredFrequencyType,oneof" json:"preferred_frequency_type,omitempty"`
	// preferred frequency type v2
	PreferredFrequencyTypeV2 *int32 `protobuf:"varint,17,opt,name=preferred_frequency_type_v2,json=preferredFrequencyTypeV2,proto3,oneof" json:"preferred_frequency_type_v2,omitempty"`
	// preferred day of week, 0-6, Sunday-Saturday
	PreferredDay *v2.Int32List `protobuf:"bytes,15,opt,name=preferred_day,json=preferredDay,proto3,oneof" json:"preferred_day,omitempty"`
	// preferred time of day, minutes
	PreferredTime *v2.Int32List `protobuf:"bytes,16,opt,name=preferred_time,json=preferredTime,proto3,oneof" json:"preferred_time,omitempty"`
}

func (x *BusinessCustomerUpdateDef) Reset() {
	*x = BusinessCustomerUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerUpdateDef) ProtoMessage() {}

func (x *BusinessCustomerUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerUpdateDef.ProtoReflect.Descriptor instead.
func (*BusinessCustomerUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessCustomerUpdateDef) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *BusinessCustomerUpdateDef) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *BusinessCustomerUpdateDef) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *BusinessCustomerUpdateDef) GetCustomerTags() *BusinessCustomerUpdateDef_CustomerTagList {
	if x != nil {
		return x.CustomerTags
	}
	return nil
}

func (x *BusinessCustomerUpdateDef) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *BusinessCustomerUpdateDef) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *BusinessCustomerUpdateDef) GetAddress() *BusinessCustomerAddressUpdateDef {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *BusinessCustomerUpdateDef) GetPreferredGroomerId() int64 {
	if x != nil && x.PreferredGroomerId != nil {
		return *x.PreferredGroomerId
	}
	return 0
}

func (x *BusinessCustomerUpdateDef) GetEmergencyContact() *BusinessCustomerUpdateDef_EmergencyContact {
	if x != nil {
		return x.EmergencyContact
	}
	return nil
}

func (x *BusinessCustomerUpdateDef) GetPickupContact() *BusinessCustomerUpdateDef_PickupContact {
	if x != nil {
		return x.PickupContact
	}
	return nil
}

func (x *BusinessCustomerUpdateDef) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *BusinessCustomerUpdateDef) GetReferralSourceId() int32 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *BusinessCustomerUpdateDef) GetPreferredFrequencyDay() int32 {
	if x != nil && x.PreferredFrequencyDay != nil {
		return *x.PreferredFrequencyDay
	}
	return 0
}

// Deprecated: Do not use.
func (x *BusinessCustomerUpdateDef) GetPreferredFrequencyType() v1.PreferredFrequencyType {
	if x != nil && x.PreferredFrequencyType != nil {
		return *x.PreferredFrequencyType
	}
	return v1.PreferredFrequencyType(0)
}

func (x *BusinessCustomerUpdateDef) GetPreferredFrequencyTypeV2() int32 {
	if x != nil && x.PreferredFrequencyTypeV2 != nil {
		return *x.PreferredFrequencyTypeV2
	}
	return 0
}

func (x *BusinessCustomerUpdateDef) GetPreferredDay() *v2.Int32List {
	if x != nil {
		return x.PreferredDay
	}
	return nil
}

func (x *BusinessCustomerUpdateDef) GetPreferredTime() *v2.Int32List {
	if x != nil {
		return x.PreferredTime
	}
	return nil
}

// customer tag list
type BusinessCustomerUpdateDef_CustomerTagList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer tag ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *BusinessCustomerUpdateDef_CustomerTagList) Reset() {
	*x = BusinessCustomerUpdateDef_CustomerTagList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerUpdateDef_CustomerTagList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerUpdateDef_CustomerTagList) ProtoMessage() {}

func (x *BusinessCustomerUpdateDef_CustomerTagList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerUpdateDef_CustomerTagList.ProtoReflect.Descriptor instead.
func (*BusinessCustomerUpdateDef_CustomerTagList) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescGZIP(), []int{2, 0}
}

func (x *BusinessCustomerUpdateDef_CustomerTagList) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// emergency contact
type BusinessCustomerUpdateDef_EmergencyContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer contact id, optional
	Id *int64 `protobuf:"varint,4,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// first name
	FirstName *string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name
	LastName *string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// phone number
	PhoneNumber *string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
}

func (x *BusinessCustomerUpdateDef_EmergencyContact) Reset() {
	*x = BusinessCustomerUpdateDef_EmergencyContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerUpdateDef_EmergencyContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerUpdateDef_EmergencyContact) ProtoMessage() {}

func (x *BusinessCustomerUpdateDef_EmergencyContact) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerUpdateDef_EmergencyContact.ProtoReflect.Descriptor instead.
func (*BusinessCustomerUpdateDef_EmergencyContact) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescGZIP(), []int{2, 1}
}

func (x *BusinessCustomerUpdateDef_EmergencyContact) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *BusinessCustomerUpdateDef_EmergencyContact) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *BusinessCustomerUpdateDef_EmergencyContact) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *BusinessCustomerUpdateDef_EmergencyContact) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

// pickup contact
type BusinessCustomerUpdateDef_PickupContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer contact id, optional
	Id *int64 `protobuf:"varint,4,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// first name
	FirstName *string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name
	LastName *string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// phone number
	PhoneNumber *string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
}

func (x *BusinessCustomerUpdateDef_PickupContact) Reset() {
	*x = BusinessCustomerUpdateDef_PickupContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerUpdateDef_PickupContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerUpdateDef_PickupContact) ProtoMessage() {}

func (x *BusinessCustomerUpdateDef_PickupContact) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerUpdateDef_PickupContact.ProtoReflect.Descriptor instead.
func (*BusinessCustomerUpdateDef_PickupContact) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescGZIP(), []int{2, 2}
}

func (x *BusinessCustomerUpdateDef_PickupContact) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *BusinessCustomerUpdateDef_PickupContact) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *BusinessCustomerUpdateDef_PickupContact) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *BusinessCustomerUpdateDef_PickupContact) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

var File_moego_models_business_customer_v1_business_customer_defs_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_customer_defs_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x46, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x49, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xc0, 0x08, 0x0a, 0x19, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x75, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x82, 0x01, 0x12, 0x10, 0x01, 0x20, 0x00,
	0x20, 0x01, 0x20, 0x02, 0x20, 0x03, 0x20, 0x04, 0x20, 0x05, 0x20, 0x06, 0x20, 0x07, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x14, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1e, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x2f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x72, 0x09, 0x18, 0xff,
	0x01, 0xd0, 0x01, 0x01, 0x88, 0x01, 0x01, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32,
	0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2f, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32,
	0x48, 0x00, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x88,
	0x01, 0x01, 0x12, 0x40, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x13, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x02, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x3a, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x03, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a,
	0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10,
	0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x14, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48, 0x04, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32,
	0x00, 0x48, 0x05, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x48, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32, 0x00, 0x48, 0x06, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x08, 0x62,
	0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x07, 0x52, 0x08, 0x62, 0x69, 0x72,
	0x74, 0x68, 0x64, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x88, 0x01, 0x01, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x42, 0x18, 0x0a, 0x16, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x42, 0x17, 0x0a, 0x15, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x22, 0xfe, 0x07, 0x0a, 0x2b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x12, 0x62, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x93, 0x01, 0x0a, 0x18, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x48, 0x00, 0x52, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x8d,
	0x01, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x66, 0x48, 0x01, 0x52, 0x15, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x81,
	0x01, 0x0a, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x11, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x71, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x48, 0x03, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x80, 0x01, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x10, 0x14, 0x52, 0x13, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x68, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x10, 0xc8, 0x01, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42,
	0x19, 0x0a, 0x17, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x8a, 0x11, 0x0a, 0x19, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x12, 0x34, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x72, 0x09, 0x18,
	0xff, 0x01, 0xd0, 0x01, 0x01, 0x88, 0x01, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x32, 0x48, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x76, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x03, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x54, 0x61, 0x67, 0x73, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x32, 0x48, 0x04, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a,
	0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x05, 0x52, 0x08, 0x62,
	0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x62, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x48, 0x06, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3e,
	0x0a, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x07, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x7f,
	0x0a, 0x11, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x45, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x48, 0x08, 0x52, 0x10, 0x65, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x76, 0x0a, 0x0e, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x48, 0x09, 0x52, 0x0d, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x0a, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x0b, 0x52,
	0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x0c,
	0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x7d, 0x0a, 0x18, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c,
	0x18, 0x01, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0d, 0x52, 0x16,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x1b, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x32, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x0e, 0x52, 0x18, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x56, 0x32, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x0f, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x0e, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x10,
	0x52, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x1a, 0x35, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x14, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x1a, 0xee, 0x01, 0x0a, 0x10, 0x45, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x1c,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52, 0x09, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x48, 0x03, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x1a, 0xeb, 0x01, 0x0a, 0x0d, 0x50,
	0x69, 0x63, 0x6b, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x1c, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x48, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x32, 0x48, 0x03, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x69,
	0x63, 0x6b, 0x75, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x0f, 0x0a, 0x0d,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x15, 0x0a,
	0x13, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79,
	0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1e, 0x0a,
	0x1c, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x32, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescData = file_moego_models_business_customer_v1_business_customer_defs_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_customer_defs_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_models_business_customer_v1_business_customer_defs_proto_goTypes = []interface{}{
	(*BusinessCustomerCreateDef)(nil),                        // 0: moego.models.business_customer.v1.BusinessCustomerCreateDef
	(*BusinessCustomerWithAdditionalInfoCreateDef)(nil),      // 1: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef
	(*BusinessCustomerUpdateDef)(nil),                        // 2: moego.models.business_customer.v1.BusinessCustomerUpdateDef
	(*BusinessCustomerUpdateDef_CustomerTagList)(nil),        // 3: moego.models.business_customer.v1.BusinessCustomerUpdateDef.CustomerTagList
	(*BusinessCustomerUpdateDef_EmergencyContact)(nil),       // 4: moego.models.business_customer.v1.BusinessCustomerUpdateDef.EmergencyContact
	(*BusinessCustomerUpdateDef_PickupContact)(nil),          // 5: moego.models.business_customer.v1.BusinessCustomerUpdateDef.PickupContact
	(BusinessCustomerInfoModel_Source)(0),                    // 6: moego.models.business_customer.v1.BusinessCustomerInfoModel.Source
	(*timestamppb.Timestamp)(nil),                            // 7: google.protobuf.Timestamp
	(*BusinessCustomerCommunicationPreferenceUpdateDef)(nil), // 8: moego.models.business_customer.v1.BusinessCustomerCommunicationPreferenceUpdateDef
	(*BusinessCustomerAppointmentPreferenceUpdateDef)(nil),   // 9: moego.models.business_customer.v1.BusinessCustomerAppointmentPreferenceUpdateDef
	(*BusinessCustomerPaymentPreferenceUpdateDef)(nil),       // 10: moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceUpdateDef
	(*BusinessCustomerAddressCreateDef)(nil),                 // 11: moego.models.business_customer.v1.BusinessCustomerAddressCreateDef
	(*BusinessCustomerNoteCreateDef)(nil),                    // 12: moego.models.business_customer.v1.BusinessCustomerNoteCreateDef
	(*BusinessCustomerAddressUpdateDef)(nil),                 // 13: moego.models.business_customer.v1.BusinessCustomerAddressUpdateDef
	(v1.PreferredFrequencyType)(0),                           // 14: moego.models.customer.v1.PreferredFrequencyType
	(*v2.Int32List)(nil),                                     // 15: moego.utils.v2.Int32List
}
var file_moego_models_business_customer_v1_business_customer_defs_proto_depIdxs = []int32{
	6,  // 0: moego.models.business_customer.v1.BusinessCustomerCreateDef.source:type_name -> moego.models.business_customer.v1.BusinessCustomerInfoModel.Source
	7,  // 1: moego.models.business_customer.v1.BusinessCustomerCreateDef.created_at:type_name -> google.protobuf.Timestamp
	7,  // 2: moego.models.business_customer.v1.BusinessCustomerCreateDef.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 3: moego.models.business_customer.v1.BusinessCustomerCreateDef.birthday:type_name -> google.protobuf.Timestamp
	0,  // 4: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerCreateDef
	8,  // 5: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef.communication_preference:type_name -> moego.models.business_customer.v1.BusinessCustomerCommunicationPreferenceUpdateDef
	9,  // 6: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef.appointment_preference:type_name -> moego.models.business_customer.v1.BusinessCustomerAppointmentPreferenceUpdateDef
	10, // 7: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef.payment_preference:type_name -> moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceUpdateDef
	11, // 8: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef.primary_address:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressCreateDef
	11, // 9: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef.additional_addresses:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressCreateDef
	12, // 10: moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef.notes:type_name -> moego.models.business_customer.v1.BusinessCustomerNoteCreateDef
	3,  // 11: moego.models.business_customer.v1.BusinessCustomerUpdateDef.customer_tags:type_name -> moego.models.business_customer.v1.BusinessCustomerUpdateDef.CustomerTagList
	7,  // 12: moego.models.business_customer.v1.BusinessCustomerUpdateDef.birthday:type_name -> google.protobuf.Timestamp
	13, // 13: moego.models.business_customer.v1.BusinessCustomerUpdateDef.address:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressUpdateDef
	4,  // 14: moego.models.business_customer.v1.BusinessCustomerUpdateDef.emergency_contact:type_name -> moego.models.business_customer.v1.BusinessCustomerUpdateDef.EmergencyContact
	5,  // 15: moego.models.business_customer.v1.BusinessCustomerUpdateDef.pickup_contact:type_name -> moego.models.business_customer.v1.BusinessCustomerUpdateDef.PickupContact
	14, // 16: moego.models.business_customer.v1.BusinessCustomerUpdateDef.preferred_frequency_type:type_name -> moego.models.customer.v1.PreferredFrequencyType
	15, // 17: moego.models.business_customer.v1.BusinessCustomerUpdateDef.preferred_day:type_name -> moego.utils.v2.Int32List
	15, // 18: moego.models.business_customer.v1.BusinessCustomerUpdateDef.preferred_time:type_name -> moego.utils.v2.Int32List
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_customer_defs_proto_init() }
func file_moego_models_business_customer_v1_business_customer_defs_proto_init() {
	if File_moego_models_business_customer_v1_business_customer_defs_proto != nil {
		return
	}
	file_moego_models_business_customer_v1_business_customer_address_defs_proto_init()
	file_moego_models_business_customer_v1_business_customer_models_proto_init()
	file_moego_models_business_customer_v1_business_customer_note_defs_proto_init()
	file_moego_models_business_customer_v1_business_customer_preference_defs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerWithAdditionalInfoCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerUpdateDef_CustomerTagList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerUpdateDef_EmergencyContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerUpdateDef_PickupContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_customer_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_customer_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_customer_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_customer_defs_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_customer_defs_proto = out.File
	file_moego_models_business_customer_v1_business_customer_defs_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_customer_defs_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_customer_defs_proto_depIdxs = nil
}
