// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/customer/v1/customer_defs.proto

package customerpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// online booking customer definition
type CustomerDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName *string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name
	LastName *string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// avatar path
	AvatarPath *string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// phone number
	PhoneNumber *string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// email
	Email *string `protobuf:"bytes,5,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// referral source id
	ReferralSourceId *int32 `protobuf:"varint,6,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// preferred groomer id
	PreferredGroomerId *int32 `protobuf:"varint,7,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3,oneof" json:"preferred_groomer_id,omitempty"`
	// preferred frequency type, 0: day, 1: week, 2: month
	PreferredFrequencyType *int32 `protobuf:"varint,8,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3,oneof" json:"preferred_frequency_type,omitempty"`
	// preferred frequency day
	PreferredFrequencyDay *int32 `protobuf:"varint,9,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3,oneof" json:"preferred_frequency_day,omitempty"`
	// preferred day of week
	// Deprecated by Freeman since 2025/7/22, use preferred_days instead
	//
	// Deprecated: Do not use.
	PreferredDay []int32 `protobuf:"varint,10,rep,packed,name=preferred_day,json=preferredDay,proto3" json:"preferred_day,omitempty"`
	// preferred time of day
	// Deprecated by Freeman since 2025/7/22, use preferred_times instead
	//
	// Deprecated: Do not use.
	PreferredTime []int32 `protobuf:"varint,11,rep,packed,name=preferred_time,json=preferredTime,proto3" json:"preferred_time,omitempty"`
	// preferred days of week
	PreferredDays *v2.Int32List `protobuf:"bytes,15,opt,name=preferred_days,json=preferredDays,proto3,oneof" json:"preferred_days,omitempty"`
	// preferred times of day
	PreferredTimes *v2.Int32List `protobuf:"bytes,16,opt,name=preferred_times,json=preferredTimes,proto3,oneof" json:"preferred_times,omitempty"`
	// emergency contact
	EmergencyContact *CustomerDef_Contact `protobuf:"bytes,12,opt,name=emergency_contact,json=emergencyContact,proto3" json:"emergency_contact,omitempty"`
	// pickup contact
	PickupContact *CustomerDef_Contact `protobuf:"bytes,13,opt,name=pickup_contact,json=pickupContact,proto3" json:"pickup_contact,omitempty"`
	// birthday, 只有定义没有实现，birthday 暂时不支持 C 端修改/删除
	// 原因是 Timestamp 没有一个合适的值适合用于表示删除，Timestamp 的零值（1970-01-01T00:00:00Z）对前端太不友好
	// 并且 C 端删除 birthday 的能力会依赖于 B 端，B 端暂时不支持删除 birthday
	Birthday *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
}

func (x *CustomerDef) Reset() {
	*x = CustomerDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_customer_v1_customer_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDef) ProtoMessage() {}

func (x *CustomerDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_customer_v1_customer_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDef.ProtoReflect.Descriptor instead.
func (*CustomerDef) Descriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerDef) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *CustomerDef) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *CustomerDef) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *CustomerDef) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *CustomerDef) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CustomerDef) GetReferralSourceId() int32 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *CustomerDef) GetPreferredGroomerId() int32 {
	if x != nil && x.PreferredGroomerId != nil {
		return *x.PreferredGroomerId
	}
	return 0
}

func (x *CustomerDef) GetPreferredFrequencyType() int32 {
	if x != nil && x.PreferredFrequencyType != nil {
		return *x.PreferredFrequencyType
	}
	return 0
}

func (x *CustomerDef) GetPreferredFrequencyDay() int32 {
	if x != nil && x.PreferredFrequencyDay != nil {
		return *x.PreferredFrequencyDay
	}
	return 0
}

// Deprecated: Do not use.
func (x *CustomerDef) GetPreferredDay() []int32 {
	if x != nil {
		return x.PreferredDay
	}
	return nil
}

// Deprecated: Do not use.
func (x *CustomerDef) GetPreferredTime() []int32 {
	if x != nil {
		return x.PreferredTime
	}
	return nil
}

func (x *CustomerDef) GetPreferredDays() *v2.Int32List {
	if x != nil {
		return x.PreferredDays
	}
	return nil
}

func (x *CustomerDef) GetPreferredTimes() *v2.Int32List {
	if x != nil {
		return x.PreferredTimes
	}
	return nil
}

func (x *CustomerDef) GetEmergencyContact() *CustomerDef_Contact {
	if x != nil {
		return x.EmergencyContact
	}
	return nil
}

func (x *CustomerDef) GetPickupContact() *CustomerDef_Contact {
	if x != nil {
		return x.PickupContact
	}
	return nil
}

func (x *CustomerDef) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

// contact
type CustomerDef_Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *CustomerDef_Contact) Reset() {
	*x = CustomerDef_Contact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_customer_v1_customer_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDef_Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDef_Contact) ProtoMessage() {}

func (x *CustomerDef_Contact) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_customer_v1_customer_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDef_Contact.ProtoReflect.Descriptor instead.
func (*CustomerDef_Contact) Descriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_defs_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CustomerDef_Contact) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerDef_Contact) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerDef_Contact) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

var File_moego_models_customer_v1_customer_defs_proto protoreflect.FileDescriptor

var file_moego_models_customer_v1_customer_defs_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xce, 0x0a,
	0x0a, 0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x66, 0x12, 0x2b, 0x0a,
	0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61,
	0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x1e, 0x48, 0x03, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x04,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x12, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48,
	0x05, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x06, 0x52,
	0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x18, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x30,
	0x00, 0x30, 0x01, 0x30, 0x02, 0x48, 0x07, 0x52, 0x16, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x3b, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x08, 0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x37, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x05, 0x42, 0x12, 0x18, 0x01, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a,
	0x10, 0x07, 0x22, 0x06, 0x1a, 0x04, 0x18, 0x06, 0x28, 0x00, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x61, 0x79, 0x12, 0x3a, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x05,
	0x42, 0x13, 0x18, 0x01, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0x02, 0x22, 0x07, 0x1a, 0x05,
	0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x44, 0x61, 0x79, 0x73, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a, 0x0f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x4c, 0x69, 0x73, 0x74, 0x48,
	0x0a, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x11, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x44, 0x65, 0x66, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x10,
	0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x12, 0x54, 0x0a, 0x0e, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x66, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x0d, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x3b, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64,
	0x61, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x0b, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x88, 0x01, 0x01, 0x1a, 0x83, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12,
	0x26, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1e, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x1a,
	0x0a, 0x18, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x42, 0x7e,
	0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_customer_v1_customer_defs_proto_rawDescOnce sync.Once
	file_moego_models_customer_v1_customer_defs_proto_rawDescData = file_moego_models_customer_v1_customer_defs_proto_rawDesc
)

func file_moego_models_customer_v1_customer_defs_proto_rawDescGZIP() []byte {
	file_moego_models_customer_v1_customer_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_customer_v1_customer_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_customer_v1_customer_defs_proto_rawDescData)
	})
	return file_moego_models_customer_v1_customer_defs_proto_rawDescData
}

var file_moego_models_customer_v1_customer_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_customer_v1_customer_defs_proto_goTypes = []interface{}{
	(*CustomerDef)(nil),           // 0: moego.models.customer.v1.CustomerDef
	(*CustomerDef_Contact)(nil),   // 1: moego.models.customer.v1.CustomerDef.Contact
	(*v2.Int32List)(nil),          // 2: moego.utils.v2.Int32List
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_moego_models_customer_v1_customer_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.customer.v1.CustomerDef.preferred_days:type_name -> moego.utils.v2.Int32List
	2, // 1: moego.models.customer.v1.CustomerDef.preferred_times:type_name -> moego.utils.v2.Int32List
	1, // 2: moego.models.customer.v1.CustomerDef.emergency_contact:type_name -> moego.models.customer.v1.CustomerDef.Contact
	1, // 3: moego.models.customer.v1.CustomerDef.pickup_contact:type_name -> moego.models.customer.v1.CustomerDef.Contact
	3, // 4: moego.models.customer.v1.CustomerDef.birthday:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_customer_v1_customer_defs_proto_init() }
func file_moego_models_customer_v1_customer_defs_proto_init() {
	if File_moego_models_customer_v1_customer_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_customer_v1_customer_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_customer_v1_customer_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDef_Contact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_customer_v1_customer_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_customer_v1_customer_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_customer_v1_customer_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_customer_v1_customer_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_customer_v1_customer_defs_proto_msgTypes,
	}.Build()
	File_moego_models_customer_v1_customer_defs_proto = out.File
	file_moego_models_customer_v1_customer_defs_proto_rawDesc = nil
	file_moego_models_customer_v1_customer_defs_proto_goTypes = nil
	file_moego_models_customer_v1_customer_defs_proto_depIdxs = nil
}
