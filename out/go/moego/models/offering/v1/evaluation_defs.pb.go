// @since 2024-03-12 11:33:59
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/evaluation_defs.proto

package offeringpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The Evaluation Definition
// Note:
//  1. a def message must end with Def
//  2. a def message could be used in both request and response
//  3. all the fields of a def message must be validated
//  4. a def message's semantics must be single, which means you
//     cannot share a single def message when anyone field is
//     different, including labels.
type EvaluationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether the evaluation is available for all business
	AvailableForAllBusiness bool `protobuf:"varint,1,opt,name=available_for_all_business,json=availableForAllBusiness,proto3" json:"available_for_all_business,omitempty"`
	// available business ids (if available_for_all_business is false)
	AvailableBusinessIds []int64 `protobuf:"varint,2,rep,packed,name=available_business_ids,json=availableBusinessIds,proto3" json:"available_business_ids,omitempty"`
	// service item types that require evaluation
	ServiceItemTypes []ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price
	Price *float64 `protobuf:"fixed64,4,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,6,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// name
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// whether the service is available for all lodging
	LodgingFilter bool `protobuf:"varint,9,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if available_for_all_lodgings is false)
	CustomizedLodgingIds []int64 `protobuf:"varint,10,rep,packed,name=customized_lodging_ids,json=customizedLodgingIds,proto3" json:"customized_lodging_ids,omitempty"`
	// description
	Description string `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	// name shown in ob flow
	AliasForOnlineBooking *string `protobuf:"bytes,12,opt,name=alias_for_online_booking,json=aliasForOnlineBooking,proto3,oneof" json:"alias_for_online_booking,omitempty"`
	// is available for all staff. default is true
	IsAllStaff *bool `protobuf:"varint,13,opt,name=is_all_staff,json=isAllStaff,proto3,oneof" json:"is_all_staff,omitempty"`
	// available staff ids(only if is_all_staff is false)
	AllowedStaffList []int64 `protobuf:"varint,14,rep,packed,name=allowed_staff_list,json=allowedStaffList,proto3" json:"allowed_staff_list,omitempty"`
	// is allow staff auto assign. default is false
	AllowStaffAutoAssign *bool `protobuf:"varint,15,opt,name=allow_staff_auto_assign,json=allowStaffAutoAssign,proto3,oneof" json:"allow_staff_auto_assign,omitempty"`
	// is resettable
	IsResettable *bool `protobuf:"varint,16,opt,name=is_resettable,json=isResettable,proto3,oneof" json:"is_resettable,omitempty"`
	// reset interval days
	ResetIntervalDays *int32 `protobuf:"varint,17,opt,name=reset_interval_days,json=resetIntervalDays,proto3,oneof" json:"reset_interval_days,omitempty"`
	// Pet type breed filter
	BreedFilterConfig *PetBreedFilterConfig `protobuf:"bytes,18,opt,name=breed_filter_config,json=breedFilterConfig,proto3,oneof" json:"breed_filter_config,omitempty"`
	// tax_id
	TaxId *int64 `protobuf:"varint,20,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
}

func (x *EvaluationDef) Reset() {
	*x = EvaluationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_evaluation_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationDef) ProtoMessage() {}

func (x *EvaluationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_evaluation_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationDef.ProtoReflect.Descriptor instead.
func (*EvaluationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_evaluation_defs_proto_rawDescGZIP(), []int{0}
}

func (x *EvaluationDef) GetAvailableForAllBusiness() bool {
	if x != nil {
		return x.AvailableForAllBusiness
	}
	return false
}

func (x *EvaluationDef) GetAvailableBusinessIds() []int64 {
	if x != nil {
		return x.AvailableBusinessIds
	}
	return nil
}

func (x *EvaluationDef) GetServiceItemTypes() []ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *EvaluationDef) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *EvaluationDef) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *EvaluationDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *EvaluationDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationDef) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *EvaluationDef) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *EvaluationDef) GetCustomizedLodgingIds() []int64 {
	if x != nil {
		return x.CustomizedLodgingIds
	}
	return nil
}

func (x *EvaluationDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EvaluationDef) GetAliasForOnlineBooking() string {
	if x != nil && x.AliasForOnlineBooking != nil {
		return *x.AliasForOnlineBooking
	}
	return ""
}

func (x *EvaluationDef) GetIsAllStaff() bool {
	if x != nil && x.IsAllStaff != nil {
		return *x.IsAllStaff
	}
	return false
}

func (x *EvaluationDef) GetAllowedStaffList() []int64 {
	if x != nil {
		return x.AllowedStaffList
	}
	return nil
}

func (x *EvaluationDef) GetAllowStaffAutoAssign() bool {
	if x != nil && x.AllowStaffAutoAssign != nil {
		return *x.AllowStaffAutoAssign
	}
	return false
}

func (x *EvaluationDef) GetIsResettable() bool {
	if x != nil && x.IsResettable != nil {
		return *x.IsResettable
	}
	return false
}

func (x *EvaluationDef) GetResetIntervalDays() int32 {
	if x != nil && x.ResetIntervalDays != nil {
		return *x.ResetIntervalDays
	}
	return 0
}

func (x *EvaluationDef) GetBreedFilterConfig() *PetBreedFilterConfig {
	if x != nil {
		return x.BreedFilterConfig
	}
	return nil
}

func (x *EvaluationDef) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

// The message for pet breed filter config
type PetBreedFilterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether the service is available for all pet type & breed
	BreedFilter bool `protobuf:"varint,1,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
	Filters []*PetBreedFilter `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *PetBreedFilterConfig) Reset() {
	*x = PetBreedFilterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_evaluation_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetBreedFilterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBreedFilterConfig) ProtoMessage() {}

func (x *PetBreedFilterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_evaluation_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBreedFilterConfig.ProtoReflect.Descriptor instead.
func (*PetBreedFilterConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_evaluation_defs_proto_rawDescGZIP(), []int{1}
}

func (x *PetBreedFilterConfig) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *PetBreedFilterConfig) GetFilters() []*PetBreedFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

var File_moego_models_offering_v1_evaluation_defs_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_evaluation_defs_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x84, 0x09, 0x0a, 0x0d, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x29, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48,
	0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x23, 0x28,
	0x5b, 0x41, 0x2d, 0x46, 0x61, 0x2d, 0x66, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x7d, 0x29, 0x24,
	0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x64, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x34,
	0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xdc, 0x0b, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x45, 0x0a, 0x18, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48, 0x01, 0x52, 0x15, 0x61,
	0x6c, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52,
	0x0a, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x88, 0x01, 0x01, 0x12, 0x2c,
	0x0a, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x17,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52,
	0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x75, 0x74, 0x6f, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x04, 0x52, 0x0c, 0x69, 0x73, 0x52, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x05, 0x52, 0x11, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x44, 0x61, 0x79, 0x73, 0x88, 0x01, 0x01, 0x12, 0x63, 0x0a, 0x13, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x48, 0x06, 0x52, 0x11, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06,
	0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x48, 0x07, 0x52, 0x05,
	0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72,
	0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x16,
	0x0a, 0x14, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x22, 0x89, 0x01, 0x0a, 0x14, 0x50, 0x65,
	0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x10, 0x64, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_evaluation_defs_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_evaluation_defs_proto_rawDescData = file_moego_models_offering_v1_evaluation_defs_proto_rawDesc
)

func file_moego_models_offering_v1_evaluation_defs_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_evaluation_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_evaluation_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_evaluation_defs_proto_rawDescData)
	})
	return file_moego_models_offering_v1_evaluation_defs_proto_rawDescData
}

var file_moego_models_offering_v1_evaluation_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_offering_v1_evaluation_defs_proto_goTypes = []interface{}{
	(*EvaluationDef)(nil),        // 0: moego.models.offering.v1.EvaluationDef
	(*PetBreedFilterConfig)(nil), // 1: moego.models.offering.v1.PetBreedFilterConfig
	(ServiceItemType)(0),         // 2: moego.models.offering.v1.ServiceItemType
	(*PetBreedFilter)(nil),       // 3: moego.models.offering.v1.PetBreedFilter
}
var file_moego_models_offering_v1_evaluation_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.offering.v1.EvaluationDef.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	1, // 1: moego.models.offering.v1.EvaluationDef.breed_filter_config:type_name -> moego.models.offering.v1.PetBreedFilterConfig
	3, // 2: moego.models.offering.v1.PetBreedFilterConfig.filters:type_name -> moego.models.offering.v1.PetBreedFilter
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_evaluation_defs_proto_init() }
func file_moego_models_offering_v1_evaluation_defs_proto_init() {
	if File_moego_models_offering_v1_evaluation_defs_proto != nil {
		return
	}
	file_moego_models_offering_v1_evaluation_models_proto_init()
	file_moego_models_offering_v1_service_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_evaluation_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_evaluation_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetBreedFilterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v1_evaluation_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_evaluation_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_evaluation_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_evaluation_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v1_evaluation_defs_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_evaluation_defs_proto = out.File
	file_moego_models_offering_v1_evaluation_defs_proto_rawDesc = nil
	file_moego_models_offering_v1_evaluation_defs_proto_goTypes = nil
	file_moego_models_offering_v1_evaluation_defs_proto_depIdxs = nil
}
