// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/finance_gw/v1/webhook_service.proto

package financegwsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WebhookServiceClient is the client API for WebhookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebhookServiceClient interface {
	// Handle webhook event. The raw event body is received.
	HandleRawEvent(ctx context.Context, in *HandleRawEventRequest, opts ...grpc.CallOption) (*HandleRawEventResponse, error)
	// Task Retry Webhook
	TaskRetryWebhook(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type webhookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWebhookServiceClient(cc grpc.ClientConnInterface) WebhookServiceClient {
	return &webhookServiceClient{cc}
}

func (c *webhookServiceClient) HandleRawEvent(ctx context.Context, in *HandleRawEventRequest, opts ...grpc.CallOption) (*HandleRawEventResponse, error) {
	out := new(HandleRawEventResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_gw.v1.WebhookService/HandleRawEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookServiceClient) TaskRetryWebhook(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.finance_gw.v1.WebhookService/TaskRetryWebhook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebhookServiceServer is the server API for WebhookService service.
// All implementations must embed UnimplementedWebhookServiceServer
// for forward compatibility
type WebhookServiceServer interface {
	// Handle webhook event. The raw event body is received.
	HandleRawEvent(context.Context, *HandleRawEventRequest) (*HandleRawEventResponse, error)
	// Task Retry Webhook
	TaskRetryWebhook(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedWebhookServiceServer()
}

// UnimplementedWebhookServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWebhookServiceServer struct {
}

func (UnimplementedWebhookServiceServer) HandleRawEvent(context.Context, *HandleRawEventRequest) (*HandleRawEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleRawEvent not implemented")
}
func (UnimplementedWebhookServiceServer) TaskRetryWebhook(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskRetryWebhook not implemented")
}
func (UnimplementedWebhookServiceServer) mustEmbedUnimplementedWebhookServiceServer() {}

// UnsafeWebhookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebhookServiceServer will
// result in compilation errors.
type UnsafeWebhookServiceServer interface {
	mustEmbedUnimplementedWebhookServiceServer()
}

func RegisterWebhookServiceServer(s grpc.ServiceRegistrar, srv WebhookServiceServer) {
	s.RegisterService(&WebhookService_ServiceDesc, srv)
}

func _WebhookService_HandleRawEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleRawEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).HandleRawEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_gw.v1.WebhookService/HandleRawEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).HandleRawEvent(ctx, req.(*HandleRawEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookService_TaskRetryWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).TaskRetryWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_gw.v1.WebhookService/TaskRetryWebhook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).TaskRetryWebhook(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// WebhookService_ServiceDesc is the grpc.ServiceDesc for WebhookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WebhookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.finance_gw.v1.WebhookService",
	HandlerType: (*WebhookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleRawEvent",
			Handler:    _WebhookService_HandleRawEvent_Handler,
		},
		{
			MethodName: "TaskRetryWebhook",
			Handler:    _WebhookService_TaskRetryWebhook_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/finance_gw/v1/webhook_service.proto",
}
