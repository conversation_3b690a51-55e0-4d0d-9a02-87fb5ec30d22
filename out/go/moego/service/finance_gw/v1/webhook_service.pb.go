// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/finance_gw/v1/webhook_service.proto

package financegwsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_gw/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for HandleRawEvent. Ideally, most HTTP data should be passed in this request.
type HandleRawEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Webhook event from HTTP outbound.
	HttpWebhookEvent *v1.HttpWebhookEvent `protobuf:"bytes,1,opt,name=http_webhook_event,json=httpWebhookEvent,proto3" json:"http_webhook_event,omitempty"`
}

func (x *HandleRawEventRequest) Reset() {
	*x = HandleRawEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleRawEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleRawEventRequest) ProtoMessage() {}

func (x *HandleRawEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleRawEventRequest.ProtoReflect.Descriptor instead.
func (*HandleRawEventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_gw_v1_webhook_service_proto_rawDescGZIP(), []int{0}
}

func (x *HandleRawEventRequest) GetHttpWebhookEvent() *v1.HttpWebhookEvent {
	if x != nil {
		return x.HttpWebhookEvent
	}
	return nil
}

// Response for HandleRawEvent
type HandleRawEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HandleRawEventResponse) Reset() {
	*x = HandleRawEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleRawEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleRawEventResponse) ProtoMessage() {}

func (x *HandleRawEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleRawEventResponse.ProtoReflect.Descriptor instead.
func (*HandleRawEventResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_gw_v1_webhook_service_proto_rawDescGZIP(), []int{1}
}

var File_moego_service_finance_gw_v1_webhook_service_proto protoreflect.FileDescriptor

var file_moego_service_finance_gw_v1_webhook_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7d, 0x0a, 0x15, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x52, 0x61, 0x77, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x64, 0x0a, 0x12, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x10, 0x68, 0x74, 0x74, 0x70, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x18, 0x0a, 0x16, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x52, 0x61, 0x77, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x32, 0xcf, 0x01, 0x0a, 0x0e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x0e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x61, 0x77,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x61, 0x77, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x61,
	0x77, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42,
	0x0a, 0x10, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x74, 0x72, 0x79, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x42, 0x88, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2f, 0x76, 0x31, 0x3b, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x67, 0x77, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_finance_gw_v1_webhook_service_proto_rawDescOnce sync.Once
	file_moego_service_finance_gw_v1_webhook_service_proto_rawDescData = file_moego_service_finance_gw_v1_webhook_service_proto_rawDesc
)

func file_moego_service_finance_gw_v1_webhook_service_proto_rawDescGZIP() []byte {
	file_moego_service_finance_gw_v1_webhook_service_proto_rawDescOnce.Do(func() {
		file_moego_service_finance_gw_v1_webhook_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_finance_gw_v1_webhook_service_proto_rawDescData)
	})
	return file_moego_service_finance_gw_v1_webhook_service_proto_rawDescData
}

var file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_finance_gw_v1_webhook_service_proto_goTypes = []interface{}{
	(*HandleRawEventRequest)(nil),  // 0: moego.service.finance_gw.v1.HandleRawEventRequest
	(*HandleRawEventResponse)(nil), // 1: moego.service.finance_gw.v1.HandleRawEventResponse
	(*v1.HttpWebhookEvent)(nil),    // 2: moego.models.finance_gw.v1.HttpWebhookEvent
	(*emptypb.Empty)(nil),          // 3: google.protobuf.Empty
}
var file_moego_service_finance_gw_v1_webhook_service_proto_depIdxs = []int32{
	2, // 0: moego.service.finance_gw.v1.HandleRawEventRequest.http_webhook_event:type_name -> moego.models.finance_gw.v1.HttpWebhookEvent
	0, // 1: moego.service.finance_gw.v1.WebhookService.HandleRawEvent:input_type -> moego.service.finance_gw.v1.HandleRawEventRequest
	3, // 2: moego.service.finance_gw.v1.WebhookService.TaskRetryWebhook:input_type -> google.protobuf.Empty
	1, // 3: moego.service.finance_gw.v1.WebhookService.HandleRawEvent:output_type -> moego.service.finance_gw.v1.HandleRawEventResponse
	3, // 4: moego.service.finance_gw.v1.WebhookService.TaskRetryWebhook:output_type -> google.protobuf.Empty
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_finance_gw_v1_webhook_service_proto_init() }
func file_moego_service_finance_gw_v1_webhook_service_proto_init() {
	if File_moego_service_finance_gw_v1_webhook_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleRawEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleRawEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_finance_gw_v1_webhook_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_finance_gw_v1_webhook_service_proto_goTypes,
		DependencyIndexes: file_moego_service_finance_gw_v1_webhook_service_proto_depIdxs,
		MessageInfos:      file_moego_service_finance_gw_v1_webhook_service_proto_msgTypes,
	}.Build()
	File_moego_service_finance_gw_v1_webhook_service_proto = out.File
	file_moego_service_finance_gw_v1_webhook_service_proto_rawDesc = nil
	file_moego_service_finance_gw_v1_webhook_service_proto_goTypes = nil
	file_moego_service_finance_gw_v1_webhook_service_proto_depIdxs = nil
}
