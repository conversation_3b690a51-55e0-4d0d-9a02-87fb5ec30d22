// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/payment/v2/payment_onboard_service.proto

package paymentsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for GetOnboard
type GetOnboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道，先强制要求传，固定传 Adyen
	ChannelType v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetOnboardRequest) Reset() {
	*x = GetOnboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardRequest) ProtoMessage() {}

func (x *GetOnboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardRequest.ProtoReflect.Descriptor instead.
func (*GetOnboardRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetOnboardRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetOnboardRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetOnboardRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetOnboard
type GetOnboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Onboard 状态：
	// - INITIAL：初始状态
	// - CONFIGURED：配置完毕，onboarding 中
	// - ONBOARDED：已完成 onboarding，待用户确认
	// - FINISHED：完成 onboarding，包括后续有 upcoming verification 的状态
	// - NOT_APPLICABLE：不适用，预留的状态，目前的接口设计不会返回这种状态
	Status v2.OnboardStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.payment.v2.OnboardStatus" json:"status,omitempty"`
	// 包含的步骤，status 为 CONFIGURED 时有效。
	Steps []*v2.OnboardStep `protobuf:"bytes,2,rep,name=steps,proto3" json:"steps,omitempty"`
	// 当前步骤下标（相对于 steps 字段）
	CurrentStep int32 `protobuf:"varint,3,opt,name=current_step,json=currentStep,proto3" json:"current_step,omitempty"`
}

func (x *GetOnboardResponse) Reset() {
	*x = GetOnboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardResponse) ProtoMessage() {}

func (x *GetOnboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardResponse.ProtoReflect.Descriptor instead.
func (*GetOnboardResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetOnboardResponse) GetStatus() v2.OnboardStatus {
	if x != nil {
		return x.Status
	}
	return v2.OnboardStatus(0)
}

func (x *GetOnboardResponse) GetSteps() []*v2.OnboardStep {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *GetOnboardResponse) GetCurrentStep() int32 {
	if x != nil {
		return x.CurrentStep
	}
	return 0
}

// Request for GetChannelAccount
type GetChannelAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user from payment
	User *v2.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetChannelAccountRequest) Reset() {
	*x = GetChannelAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAccountRequest) ProtoMessage() {}

func (x *GetChannelAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAccountRequest.ProtoReflect.Descriptor instead.
func (*GetChannelAccountRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetChannelAccountRequest) GetUser() *v2.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GetChannelAccountRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetChannelAccount
type GetChannelAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 账号的基本信息
	ChannelAccount *v2.ChannelAccount `protobuf:"bytes,1,opt,name=channel_account,json=channelAccount,proto3" json:"channel_account,omitempty"`
}

func (x *GetChannelAccountResponse) Reset() {
	*x = GetChannelAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAccountResponse) ProtoMessage() {}

func (x *GetChannelAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAccountResponse.ProtoReflect.Descriptor instead.
func (*GetChannelAccountResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetChannelAccountResponse) GetChannelAccount() *v2.ChannelAccount {
	if x != nil {
		return x.ChannelAccount
	}
	return nil
}

// Request for ProceedOnboard
type ProceedOnboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 链接内完成后跳回的地址
	ReturnUrl *string `protobuf:"bytes,4,opt,name=return_url,json=returnUrl,proto3,oneof" json:"return_url,omitempty"`
	// 在 configure 之前需要提供的渠道特定的数据。
	//
	// Types that are assignable to Params:
	//
	//	*ProceedOnboardRequest_AdyenPreConfig
	Params isProceedOnboardRequest_Params `protobuf_oneof:"params"`
}

func (x *ProceedOnboardRequest) Reset() {
	*x = ProceedOnboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProceedOnboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProceedOnboardRequest) ProtoMessage() {}

func (x *ProceedOnboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProceedOnboardRequest.ProtoReflect.Descriptor instead.
func (*ProceedOnboardRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{4}
}

func (x *ProceedOnboardRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ProceedOnboardRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ProceedOnboardRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *ProceedOnboardRequest) GetReturnUrl() string {
	if x != nil && x.ReturnUrl != nil {
		return *x.ReturnUrl
	}
	return ""
}

func (m *ProceedOnboardRequest) GetParams() isProceedOnboardRequest_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *ProceedOnboardRequest) GetAdyenPreConfig() *v2.AdyenPreConfigDef {
	if x, ok := x.GetParams().(*ProceedOnboardRequest_AdyenPreConfig); ok {
		return x.AdyenPreConfig
	}
	return nil
}

type isProceedOnboardRequest_Params interface {
	isProceedOnboardRequest_Params()
}

type ProceedOnboardRequest_AdyenPreConfig struct {
	// Adyen 的 pre configuration，只有 OnboardStatus 为 INITIAL 状态时会被使用。
	AdyenPreConfig *v2.AdyenPreConfigDef `protobuf:"bytes,5,opt,name=adyen_pre_config,json=adyenPreConfig,proto3,oneof"`
}

func (*ProceedOnboardRequest_AdyenPreConfig) isProceedOnboardRequest_Params() {}

// Response for ProceedOnboard
type ProceedOnboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链接，若有则需要跳转到该链接来推进 onboarding
	Url *string `protobuf:"bytes,1,opt,name=url,proto3,oneof" json:"url,omitempty"`
}

func (x *ProceedOnboardResponse) Reset() {
	*x = ProceedOnboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProceedOnboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProceedOnboardResponse) ProtoMessage() {}

func (x *ProceedOnboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProceedOnboardResponse.ProtoReflect.Descriptor instead.
func (*ProceedOnboardResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{5}
}

func (x *ProceedOnboardResponse) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

// Request for ConfirmOnboardFinished
type ConfirmOnboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *ConfirmOnboardRequest) Reset() {
	*x = ConfirmOnboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmOnboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmOnboardRequest) ProtoMessage() {}

func (x *ConfirmOnboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmOnboardRequest.ProtoReflect.Descriptor instead.
func (*ConfirmOnboardRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{6}
}

func (x *ConfirmOnboardRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ConfirmOnboardRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ConfirmOnboardRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for ConfirmOnboardFinished
type ConfirmOnboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConfirmOnboardResponse) Reset() {
	*x = ConfirmOnboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmOnboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmOnboardResponse) ProtoMessage() {}

func (x *ConfirmOnboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmOnboardResponse.ProtoReflect.Descriptor instead.
func (*ConfirmOnboardResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{7}
}

// Request for GetChannelAccountDetail
type GetChannelAccountDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetChannelAccountDetailRequest) Reset() {
	*x = GetChannelAccountDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAccountDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAccountDetailRequest) ProtoMessage() {}

func (x *GetChannelAccountDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAccountDetailRequest.ProtoReflect.Descriptor instead.
func (*GetChannelAccountDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetChannelAccountDetailRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetChannelAccountDetailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetChannelAccountDetailRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetChannelAccountDetail
type GetChannelAccountDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 账号的基本信息
	ChannelAccount *v2.ChannelAccount `protobuf:"bytes,1,opt,name=channel_account,json=channelAccount,proto3" json:"channel_account,omitempty"`
	// 需要关注的 KYC 验证信息
	Verifications []*v2.OnboardVerification `protobuf:"bytes,2,rep,name=verifications,proto3" json:"verifications,omitempty"`
	// Bank Accounts
	BankAccounts []*v2.BankAccount `protobuf:"bytes,3,rep,name=bank_accounts,json=bankAccounts,proto3" json:"bank_accounts,omitempty"`
}

func (x *GetChannelAccountDetailResponse) Reset() {
	*x = GetChannelAccountDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAccountDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAccountDetailResponse) ProtoMessage() {}

func (x *GetChannelAccountDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAccountDetailResponse.ProtoReflect.Descriptor instead.
func (*GetChannelAccountDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetChannelAccountDetailResponse) GetChannelAccount() *v2.ChannelAccount {
	if x != nil {
		return x.ChannelAccount
	}
	return nil
}

func (x *GetChannelAccountDetailResponse) GetVerifications() []*v2.OnboardVerification {
	if x != nil {
		return x.Verifications
	}
	return nil
}

func (x *GetChannelAccountDetailResponse) GetBankAccounts() []*v2.BankAccount {
	if x != nil {
		return x.BankAccounts
	}
	return nil
}

// Request for RequestOnboardUpdate
type RequestOnboardUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 链接内完成后跳回的地址
	ReturnUrl *string `protobuf:"bytes,4,opt,name=return_url,json=returnUrl,proto3,oneof" json:"return_url,omitempty"`
	// 各个渠道可能需要的额外参数
	//
	// Types that are assignable to ChannelParams:
	//
	//	*RequestOnboardUpdateRequest_Adyen
	ChannelParams isRequestOnboardUpdateRequest_ChannelParams `protobuf_oneof:"channel_params"`
}

func (x *RequestOnboardUpdateRequest) Reset() {
	*x = RequestOnboardUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestOnboardUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestOnboardUpdateRequest) ProtoMessage() {}

func (x *RequestOnboardUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestOnboardUpdateRequest.ProtoReflect.Descriptor instead.
func (*RequestOnboardUpdateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{10}
}

func (x *RequestOnboardUpdateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *RequestOnboardUpdateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *RequestOnboardUpdateRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *RequestOnboardUpdateRequest) GetReturnUrl() string {
	if x != nil && x.ReturnUrl != nil {
		return *x.ReturnUrl
	}
	return ""
}

func (m *RequestOnboardUpdateRequest) GetChannelParams() isRequestOnboardUpdateRequest_ChannelParams {
	if m != nil {
		return m.ChannelParams
	}
	return nil
}

func (x *RequestOnboardUpdateRequest) GetAdyen() *RequestOnboardUpdateRequest_AdyenParams {
	if x, ok := x.GetChannelParams().(*RequestOnboardUpdateRequest_Adyen); ok {
		return x.Adyen
	}
	return nil
}

type isRequestOnboardUpdateRequest_ChannelParams interface {
	isRequestOnboardUpdateRequest_ChannelParams()
}

type RequestOnboardUpdateRequest_Adyen struct {
	// Adyen
	Adyen *RequestOnboardUpdateRequest_AdyenParams `protobuf:"bytes,5,opt,name=adyen,proto3,oneof"`
}

func (*RequestOnboardUpdateRequest_Adyen) isRequestOnboardUpdateRequest_ChannelParams() {}

// Response for RequestOnboardUpdate
type RequestOnboardUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 链接，若有则需要跳转到该链接来更新 onboard 信息
	Url *string `protobuf:"bytes,1,opt,name=url,proto3,oneof" json:"url,omitempty"`
}

func (x *RequestOnboardUpdateResponse) Reset() {
	*x = RequestOnboardUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestOnboardUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestOnboardUpdateResponse) ProtoMessage() {}

func (x *RequestOnboardUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestOnboardUpdateResponse.ProtoReflect.Descriptor instead.
func (*RequestOnboardUpdateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{11}
}

func (x *RequestOnboardUpdateResponse) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

// Additional params for Adyen.
type RequestOnboardUpdateRequest_AdyenParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Additional configuration for current business.
	BusinessConfig *v2.AdyenBusinessConfig `protobuf:"bytes,1,opt,name=business_config,json=businessConfig,proto3,oneof" json:"business_config,omitempty"`
}

func (x *RequestOnboardUpdateRequest_AdyenParams) Reset() {
	*x = RequestOnboardUpdateRequest_AdyenParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestOnboardUpdateRequest_AdyenParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestOnboardUpdateRequest_AdyenParams) ProtoMessage() {}

func (x *RequestOnboardUpdateRequest_AdyenParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestOnboardUpdateRequest_AdyenParams.ProtoReflect.Descriptor instead.
func (*RequestOnboardUpdateRequest_AdyenParams) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *RequestOnboardUpdateRequest_AdyenParams) GetBusinessConfig() *v2.AdyenBusinessConfig {
	if x != nil {
		return x.BusinessConfig
	}
	return nil
}

var File_moego_service_payment_v2_payment_onboard_service_proto protoreflect.FileDescriptor

var file_moego_service_payment_v2_payment_onboard_service_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x05, 0x73, 0x74, 0x65,
	0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x65, 0x70, 0x52, 0x05,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x65, 0x70, 0x22, 0x96, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x6d, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50,
	0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xb5, 0x02, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x10, 0x61, 0x64, 0x79, 0x65, 0x6e,
	0x5f, 0x70, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65,
	0x6e, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52,
	0x0e, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42,
	0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x37, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x65, 0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x75, 0x72,
	0x6c, 0x22, 0xa0, 0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa9,
	0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x92, 0x02, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50,
	0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x52, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22,
	0xc5, 0x03, 0x0a, 0x1b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09,
	0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x05,
	0x61, 0x64, 0x79, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00,
	0x52, 0x05, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x1a, 0x7d, 0x0a, 0x0b, 0x41, 0x64, 0x79, 0x65, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5a, 0x0a, 0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52,
	0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88,
	0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x10, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x3d, 0x0a, 0x1c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x06,
	0x0a, 0x04, 0x5f, 0x75, 0x72, 0x6c, 0x32, 0x81, 0x06, 0x0a, 0x15, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x67, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x65, 0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73,
	0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50,
	0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_payment_v2_payment_onboard_service_proto_rawDescOnce sync.Once
	file_moego_service_payment_v2_payment_onboard_service_proto_rawDescData = file_moego_service_payment_v2_payment_onboard_service_proto_rawDesc
)

func file_moego_service_payment_v2_payment_onboard_service_proto_rawDescGZIP() []byte {
	file_moego_service_payment_v2_payment_onboard_service_proto_rawDescOnce.Do(func() {
		file_moego_service_payment_v2_payment_onboard_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_payment_v2_payment_onboard_service_proto_rawDescData)
	})
	return file_moego_service_payment_v2_payment_onboard_service_proto_rawDescData
}

var file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_service_payment_v2_payment_onboard_service_proto_goTypes = []interface{}{
	(*GetOnboardRequest)(nil),                       // 0: moego.service.payment.v2.GetOnboardRequest
	(*GetOnboardResponse)(nil),                      // 1: moego.service.payment.v2.GetOnboardResponse
	(*GetChannelAccountRequest)(nil),                // 2: moego.service.payment.v2.GetChannelAccountRequest
	(*GetChannelAccountResponse)(nil),               // 3: moego.service.payment.v2.GetChannelAccountResponse
	(*ProceedOnboardRequest)(nil),                   // 4: moego.service.payment.v2.ProceedOnboardRequest
	(*ProceedOnboardResponse)(nil),                  // 5: moego.service.payment.v2.ProceedOnboardResponse
	(*ConfirmOnboardRequest)(nil),                   // 6: moego.service.payment.v2.ConfirmOnboardRequest
	(*ConfirmOnboardResponse)(nil),                  // 7: moego.service.payment.v2.ConfirmOnboardResponse
	(*GetChannelAccountDetailRequest)(nil),          // 8: moego.service.payment.v2.GetChannelAccountDetailRequest
	(*GetChannelAccountDetailResponse)(nil),         // 9: moego.service.payment.v2.GetChannelAccountDetailResponse
	(*RequestOnboardUpdateRequest)(nil),             // 10: moego.service.payment.v2.RequestOnboardUpdateRequest
	(*RequestOnboardUpdateResponse)(nil),            // 11: moego.service.payment.v2.RequestOnboardUpdateResponse
	(*RequestOnboardUpdateRequest_AdyenParams)(nil), // 12: moego.service.payment.v2.RequestOnboardUpdateRequest.AdyenParams
	(v2.ChannelType)(0),                             // 13: moego.models.payment.v2.ChannelType
	(v2.OnboardStatus)(0),                           // 14: moego.models.payment.v2.OnboardStatus
	(*v2.OnboardStep)(nil),                          // 15: moego.models.payment.v2.OnboardStep
	(*v2.User)(nil),                                 // 16: moego.models.payment.v2.User
	(*v2.ChannelAccount)(nil),                       // 17: moego.models.payment.v2.ChannelAccount
	(*v2.AdyenPreConfigDef)(nil),                    // 18: moego.models.payment.v2.AdyenPreConfigDef
	(*v2.OnboardVerification)(nil),                  // 19: moego.models.payment.v2.OnboardVerification
	(*v2.BankAccount)(nil),                          // 20: moego.models.payment.v2.BankAccount
	(*v2.AdyenBusinessConfig)(nil),                  // 21: moego.models.payment.v2.AdyenBusinessConfig
}
var file_moego_service_payment_v2_payment_onboard_service_proto_depIdxs = []int32{
	13, // 0: moego.service.payment.v2.GetOnboardRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	14, // 1: moego.service.payment.v2.GetOnboardResponse.status:type_name -> moego.models.payment.v2.OnboardStatus
	15, // 2: moego.service.payment.v2.GetOnboardResponse.steps:type_name -> moego.models.payment.v2.OnboardStep
	16, // 3: moego.service.payment.v2.GetChannelAccountRequest.user:type_name -> moego.models.payment.v2.User
	13, // 4: moego.service.payment.v2.GetChannelAccountRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	17, // 5: moego.service.payment.v2.GetChannelAccountResponse.channel_account:type_name -> moego.models.payment.v2.ChannelAccount
	13, // 6: moego.service.payment.v2.ProceedOnboardRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	18, // 7: moego.service.payment.v2.ProceedOnboardRequest.adyen_pre_config:type_name -> moego.models.payment.v2.AdyenPreConfigDef
	13, // 8: moego.service.payment.v2.ConfirmOnboardRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	13, // 9: moego.service.payment.v2.GetChannelAccountDetailRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	17, // 10: moego.service.payment.v2.GetChannelAccountDetailResponse.channel_account:type_name -> moego.models.payment.v2.ChannelAccount
	19, // 11: moego.service.payment.v2.GetChannelAccountDetailResponse.verifications:type_name -> moego.models.payment.v2.OnboardVerification
	20, // 12: moego.service.payment.v2.GetChannelAccountDetailResponse.bank_accounts:type_name -> moego.models.payment.v2.BankAccount
	13, // 13: moego.service.payment.v2.RequestOnboardUpdateRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	12, // 14: moego.service.payment.v2.RequestOnboardUpdateRequest.adyen:type_name -> moego.service.payment.v2.RequestOnboardUpdateRequest.AdyenParams
	21, // 15: moego.service.payment.v2.RequestOnboardUpdateRequest.AdyenParams.business_config:type_name -> moego.models.payment.v2.AdyenBusinessConfig
	0,  // 16: moego.service.payment.v2.PaymentOnboardService.GetOnboard:input_type -> moego.service.payment.v2.GetOnboardRequest
	4,  // 17: moego.service.payment.v2.PaymentOnboardService.ProceedOnboard:input_type -> moego.service.payment.v2.ProceedOnboardRequest
	6,  // 18: moego.service.payment.v2.PaymentOnboardService.ConfirmOnboard:input_type -> moego.service.payment.v2.ConfirmOnboardRequest
	8,  // 19: moego.service.payment.v2.PaymentOnboardService.GetChannelAccountDetail:input_type -> moego.service.payment.v2.GetChannelAccountDetailRequest
	2,  // 20: moego.service.payment.v2.PaymentOnboardService.GetChannelAccount:input_type -> moego.service.payment.v2.GetChannelAccountRequest
	10, // 21: moego.service.payment.v2.PaymentOnboardService.RequestOnboardUpdate:input_type -> moego.service.payment.v2.RequestOnboardUpdateRequest
	1,  // 22: moego.service.payment.v2.PaymentOnboardService.GetOnboard:output_type -> moego.service.payment.v2.GetOnboardResponse
	5,  // 23: moego.service.payment.v2.PaymentOnboardService.ProceedOnboard:output_type -> moego.service.payment.v2.ProceedOnboardResponse
	7,  // 24: moego.service.payment.v2.PaymentOnboardService.ConfirmOnboard:output_type -> moego.service.payment.v2.ConfirmOnboardResponse
	9,  // 25: moego.service.payment.v2.PaymentOnboardService.GetChannelAccountDetail:output_type -> moego.service.payment.v2.GetChannelAccountDetailResponse
	3,  // 26: moego.service.payment.v2.PaymentOnboardService.GetChannelAccount:output_type -> moego.service.payment.v2.GetChannelAccountResponse
	11, // 27: moego.service.payment.v2.PaymentOnboardService.RequestOnboardUpdate:output_type -> moego.service.payment.v2.RequestOnboardUpdateResponse
	22, // [22:28] is the sub-list for method output_type
	16, // [16:22] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_service_payment_v2_payment_onboard_service_proto_init() }
func file_moego_service_payment_v2_payment_onboard_service_proto_init() {
	if File_moego_service_payment_v2_payment_onboard_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProceedOnboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProceedOnboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmOnboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmOnboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAccountDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAccountDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestOnboardUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestOnboardUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestOnboardUpdateRequest_AdyenParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ProceedOnboardRequest_AdyenPreConfig)(nil),
	}
	file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*RequestOnboardUpdateRequest_Adyen)(nil),
	}
	file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_payment_v2_payment_onboard_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_payment_v2_payment_onboard_service_proto_goTypes,
		DependencyIndexes: file_moego_service_payment_v2_payment_onboard_service_proto_depIdxs,
		MessageInfos:      file_moego_service_payment_v2_payment_onboard_service_proto_msgTypes,
	}.Build()
	File_moego_service_payment_v2_payment_onboard_service_proto = out.File
	file_moego_service_payment_v2_payment_onboard_service_proto_rawDesc = nil
	file_moego_service_payment_v2_payment_onboard_service_proto_goTypes = nil
	file_moego_service_payment_v2_payment_onboard_service_proto_depIdxs = nil
}
