// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/payment/v2/payment_payout_service.proto

package paymentsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for PayoutSummary
type GetPayoutSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 实体类型
	EntityType v2.EntityType `protobuf:"varint,1,opt,name=entity_type,json=entityType,proto3,enum=moego.models.payment.v2.EntityType" json:"entity_type,omitempty"`
	// 实体 id
	EntityId int64 `protobuf:"varint,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// moego_payout_id
	PayoutId int64 `protobuf:"varint,3,opt,name=payout_id,json=payoutId,proto3" json:"payout_id,omitempty"`
}

func (x *GetPayoutSummaryRequest) Reset() {
	*x = GetPayoutSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayoutSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayoutSummaryRequest) ProtoMessage() {}

func (x *GetPayoutSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayoutSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetPayoutSummaryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_payout_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetPayoutSummaryRequest) GetEntityType() v2.EntityType {
	if x != nil {
		return x.EntityType
	}
	return v2.EntityType(0)
}

func (x *GetPayoutSummaryRequest) GetEntityId() int64 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

func (x *GetPayoutSummaryRequest) GetPayoutId() int64 {
	if x != nil {
		return x.PayoutId
	}
	return 0
}

// Response for PayoutSummary
type GetPayoutSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payout
	Payout *v2.PayoutModel `protobuf:"bytes,1,opt,name=payout,proto3" json:"payout,omitempty"`
	// payments
	Payments []*v2.PayoutModel `protobuf:"bytes,2,rep,name=payments,proto3" json:"payments,omitempty"`
}

func (x *GetPayoutSummaryResponse) Reset() {
	*x = GetPayoutSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayoutSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayoutSummaryResponse) ProtoMessage() {}

func (x *GetPayoutSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayoutSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetPayoutSummaryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_payout_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetPayoutSummaryResponse) GetPayout() *v2.PayoutModel {
	if x != nil {
		return x.Payout
	}
	return nil
}

func (x *GetPayoutSummaryResponse) GetPayments() []*v2.PayoutModel {
	if x != nil {
		return x.Payments
	}
	return nil
}

// GetPayoutListRequest
type GetPayoutListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 实体类型
	EntityType v2.EntityType `protobuf:"varint,1,opt,name=entity_type,json=entityType,proto3,enum=moego.models.payment.v2.EntityType" json:"entity_type,omitempty"`
	// 实体 id
	EntityId int64 `protobuf:"varint,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// start_created_date
	StartCreatedDate int64 `protobuf:"varint,3,opt,name=start_created_date,json=startCreatedDate,proto3" json:"start_created_date,omitempty"`
	// end_created_date
	EndCreatedDate int64 `protobuf:"varint,4,opt,name=end_created_date,json=endCreatedDate,proto3" json:"end_created_date,omitempty"`
	// pagination
	Pagination *v21.PaginationRequest `protobuf:"bytes,10,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetPayoutListRequest) Reset() {
	*x = GetPayoutListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayoutListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayoutListRequest) ProtoMessage() {}

func (x *GetPayoutListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayoutListRequest.ProtoReflect.Descriptor instead.
func (*GetPayoutListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_payout_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetPayoutListRequest) GetEntityType() v2.EntityType {
	if x != nil {
		return x.EntityType
	}
	return v2.EntityType(0)
}

func (x *GetPayoutListRequest) GetEntityId() int64 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

func (x *GetPayoutListRequest) GetStartCreatedDate() int64 {
	if x != nil {
		return x.StartCreatedDate
	}
	return 0
}

func (x *GetPayoutListRequest) GetEndCreatedDate() int64 {
	if x != nil {
		return x.EndCreatedDate
	}
	return 0
}

func (x *GetPayoutListRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// GetPayoutListResponse
type GetPayoutListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payout
	Payout []*v2.PayoutModel `protobuf:"bytes,1,rep,name=payout,proto3" json:"payout,omitempty"`
	// pagination
	Pagination *v21.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetPayoutListResponse) Reset() {
	*x = GetPayoutListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayoutListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayoutListResponse) ProtoMessage() {}

func (x *GetPayoutListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayoutListResponse.ProtoReflect.Descriptor instead.
func (*GetPayoutListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_payout_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPayoutListResponse) GetPayout() []*v2.PayoutModel {
	if x != nil {
		return x.Payout
	}
	return nil
}

func (x *GetPayoutListResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_moego_service_payment_v2_payment_payout_service_proto protoreflect.FileDescriptor

var file_moego_service_payment_v2_payment_payout_service_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x44, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x64,
	0x22, 0x9a, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a,
	0x06, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x12, 0x40, 0x0a, 0x08, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x94, 0x02,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x64, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x65, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x99, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c,
	0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x12, 0x42, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0xfc, 0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x79, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_payment_v2_payment_payout_service_proto_rawDescOnce sync.Once
	file_moego_service_payment_v2_payment_payout_service_proto_rawDescData = file_moego_service_payment_v2_payment_payout_service_proto_rawDesc
)

func file_moego_service_payment_v2_payment_payout_service_proto_rawDescGZIP() []byte {
	file_moego_service_payment_v2_payment_payout_service_proto_rawDescOnce.Do(func() {
		file_moego_service_payment_v2_payment_payout_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_payment_v2_payment_payout_service_proto_rawDescData)
	})
	return file_moego_service_payment_v2_payment_payout_service_proto_rawDescData
}

var file_moego_service_payment_v2_payment_payout_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_payment_v2_payment_payout_service_proto_goTypes = []interface{}{
	(*GetPayoutSummaryRequest)(nil),  // 0: moego.service.payment.v2.GetPayoutSummaryRequest
	(*GetPayoutSummaryResponse)(nil), // 1: moego.service.payment.v2.GetPayoutSummaryResponse
	(*GetPayoutListRequest)(nil),     // 2: moego.service.payment.v2.GetPayoutListRequest
	(*GetPayoutListResponse)(nil),    // 3: moego.service.payment.v2.GetPayoutListResponse
	(v2.EntityType)(0),               // 4: moego.models.payment.v2.EntityType
	(*v2.PayoutModel)(nil),           // 5: moego.models.payment.v2.PayoutModel
	(*v21.PaginationRequest)(nil),    // 6: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),   // 7: moego.utils.v2.PaginationResponse
}
var file_moego_service_payment_v2_payment_payout_service_proto_depIdxs = []int32{
	4, // 0: moego.service.payment.v2.GetPayoutSummaryRequest.entity_type:type_name -> moego.models.payment.v2.EntityType
	5, // 1: moego.service.payment.v2.GetPayoutSummaryResponse.payout:type_name -> moego.models.payment.v2.PayoutModel
	5, // 2: moego.service.payment.v2.GetPayoutSummaryResponse.payments:type_name -> moego.models.payment.v2.PayoutModel
	4, // 3: moego.service.payment.v2.GetPayoutListRequest.entity_type:type_name -> moego.models.payment.v2.EntityType
	6, // 4: moego.service.payment.v2.GetPayoutListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	5, // 5: moego.service.payment.v2.GetPayoutListResponse.payout:type_name -> moego.models.payment.v2.PayoutModel
	7, // 6: moego.service.payment.v2.GetPayoutListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	0, // 7: moego.service.payment.v2.PayoutService.GetPayoutSummary:input_type -> moego.service.payment.v2.GetPayoutSummaryRequest
	2, // 8: moego.service.payment.v2.PayoutService.GetPayoutList:input_type -> moego.service.payment.v2.GetPayoutListRequest
	1, // 9: moego.service.payment.v2.PayoutService.GetPayoutSummary:output_type -> moego.service.payment.v2.GetPayoutSummaryResponse
	3, // 10: moego.service.payment.v2.PayoutService.GetPayoutList:output_type -> moego.service.payment.v2.GetPayoutListResponse
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_payment_v2_payment_payout_service_proto_init() }
func file_moego_service_payment_v2_payment_payout_service_proto_init() {
	if File_moego_service_payment_v2_payment_payout_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayoutSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayoutSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayoutListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_payout_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayoutListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_payment_v2_payment_payout_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_payment_v2_payment_payout_service_proto_goTypes,
		DependencyIndexes: file_moego_service_payment_v2_payment_payout_service_proto_depIdxs,
		MessageInfos:      file_moego_service_payment_v2_payment_payout_service_proto_msgTypes,
	}.Build()
	File_moego_service_payment_v2_payment_payout_service_proto = out.File
	file_moego_service_payment_v2_payment_payout_service_proto_rawDesc = nil
	file_moego_service_payment_v2_payment_payout_service_proto_goTypes = nil
	file_moego_service_payment_v2_payment_payout_service_proto_depIdxs = nil
}
