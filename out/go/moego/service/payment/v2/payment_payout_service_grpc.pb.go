// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/payment/v2/payment_payout_service.proto

package paymentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PayoutServiceClient is the client API for PayoutService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PayoutServiceClient interface {
	// Get PayoutSummary for business
	GetPayoutSummary(ctx context.Context, in *GetPayoutSummaryRequest, opts ...grpc.CallOption) (*GetPayoutSummaryResponse, error)
	// Get Payout List for business
	GetPayoutList(ctx context.Context, in *GetPayoutListRequest, opts ...grpc.CallOption) (*GetPayoutListResponse, error)
}

type payoutServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPayoutServiceClient(cc grpc.ClientConnInterface) PayoutServiceClient {
	return &payoutServiceClient{cc}
}

func (c *payoutServiceClient) GetPayoutSummary(ctx context.Context, in *GetPayoutSummaryRequest, opts ...grpc.CallOption) (*GetPayoutSummaryResponse, error) {
	out := new(GetPayoutSummaryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PayoutService/GetPayoutSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payoutServiceClient) GetPayoutList(ctx context.Context, in *GetPayoutListRequest, opts ...grpc.CallOption) (*GetPayoutListResponse, error) {
	out := new(GetPayoutListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PayoutService/GetPayoutList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PayoutServiceServer is the server API for PayoutService service.
// All implementations must embed UnimplementedPayoutServiceServer
// for forward compatibility
type PayoutServiceServer interface {
	// Get PayoutSummary for business
	GetPayoutSummary(context.Context, *GetPayoutSummaryRequest) (*GetPayoutSummaryResponse, error)
	// Get Payout List for business
	GetPayoutList(context.Context, *GetPayoutListRequest) (*GetPayoutListResponse, error)
	mustEmbedUnimplementedPayoutServiceServer()
}

// UnimplementedPayoutServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPayoutServiceServer struct {
}

func (UnimplementedPayoutServiceServer) GetPayoutSummary(context.Context, *GetPayoutSummaryRequest) (*GetPayoutSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayoutSummary not implemented")
}
func (UnimplementedPayoutServiceServer) GetPayoutList(context.Context, *GetPayoutListRequest) (*GetPayoutListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayoutList not implemented")
}
func (UnimplementedPayoutServiceServer) mustEmbedUnimplementedPayoutServiceServer() {}

// UnsafePayoutServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PayoutServiceServer will
// result in compilation errors.
type UnsafePayoutServiceServer interface {
	mustEmbedUnimplementedPayoutServiceServer()
}

func RegisterPayoutServiceServer(s grpc.ServiceRegistrar, srv PayoutServiceServer) {
	s.RegisterService(&PayoutService_ServiceDesc, srv)
}

func _PayoutService_GetPayoutSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayoutSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayoutServiceServer).GetPayoutSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PayoutService/GetPayoutSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayoutServiceServer).GetPayoutSummary(ctx, req.(*GetPayoutSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayoutService_GetPayoutList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayoutListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayoutServiceServer).GetPayoutList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PayoutService/GetPayoutList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayoutServiceServer).GetPayoutList(ctx, req.(*GetPayoutListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PayoutService_ServiceDesc is the grpc.ServiceDesc for PayoutService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PayoutService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.payment.v2.PayoutService",
	HandlerType: (*PayoutServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPayoutSummary",
			Handler:    _PayoutService_GetPayoutSummary_Handler,
		},
		{
			MethodName: "GetPayoutList",
			Handler:    _PayoutService_GetPayoutList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/payment/v2/payment_payout_service.proto",
}
