// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/payment/v2/payment_terminal_service.proto

package paymentsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for RetrieveSdkData
type RetrieveSdkDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// Request params
	//
	// Types that are assignable to Params:
	//
	//	*RetrieveSdkDataRequest_AdyenParams_
	Params isRetrieveSdkDataRequest_Params `protobuf_oneof:"params"`
}

func (x *RetrieveSdkDataRequest) Reset() {
	*x = RetrieveSdkDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataRequest) ProtoMessage() {}

func (x *RetrieveSdkDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataRequest.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{0}
}

func (x *RetrieveSdkDataRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *RetrieveSdkDataRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *RetrieveSdkDataRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (m *RetrieveSdkDataRequest) GetParams() isRetrieveSdkDataRequest_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *RetrieveSdkDataRequest) GetAdyenParams() *RetrieveSdkDataRequest_AdyenParams {
	if x, ok := x.GetParams().(*RetrieveSdkDataRequest_AdyenParams_); ok {
		return x.AdyenParams
	}
	return nil
}

type isRetrieveSdkDataRequest_Params interface {
	isRetrieveSdkDataRequest_Params()
}

type RetrieveSdkDataRequest_AdyenParams_ struct {
	// Data for Adyen
	AdyenParams *RetrieveSdkDataRequest_AdyenParams `protobuf:"bytes,4,opt,name=adyen_params,json=adyenParams,proto3,oneof"`
}

func (*RetrieveSdkDataRequest_AdyenParams_) isRetrieveSdkDataRequest_Params() {}

// Response for RetrieveSdkData
type RetrieveSdkDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付数据
	//
	// Types that are assignable to Data:
	//
	//	*RetrieveSdkDataResponse_AdyenData_
	Data isRetrieveSdkDataResponse_Data `protobuf_oneof:"data"`
}

func (x *RetrieveSdkDataResponse) Reset() {
	*x = RetrieveSdkDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataResponse) ProtoMessage() {}

func (x *RetrieveSdkDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataResponse.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{1}
}

func (m *RetrieveSdkDataResponse) GetData() isRetrieveSdkDataResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *RetrieveSdkDataResponse) GetAdyenData() *RetrieveSdkDataResponse_AdyenData {
	if x, ok := x.GetData().(*RetrieveSdkDataResponse_AdyenData_); ok {
		return x.AdyenData
	}
	return nil
}

type isRetrieveSdkDataResponse_Data interface {
	isRetrieveSdkDataResponse_Data()
}

type RetrieveSdkDataResponse_AdyenData_ struct {
	// adyen data
	AdyenData *RetrieveSdkDataResponse_AdyenData `protobuf:"bytes,1,opt,name=adyen_data,json=adyenData,proto3,oneof"`
}

func (*RetrieveSdkDataResponse_AdyenData_) isRetrieveSdkDataResponse_Data() {}

// Adyen params
type RetrieveSdkDataRequest_AdyenParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Setup token
	SetupToken string `protobuf:"bytes,1,opt,name=setup_token,json=setupToken,proto3" json:"setup_token,omitempty"`
}

func (x *RetrieveSdkDataRequest_AdyenParams) Reset() {
	*x = RetrieveSdkDataRequest_AdyenParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataRequest_AdyenParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataRequest_AdyenParams) ProtoMessage() {}

func (x *RetrieveSdkDataRequest_AdyenParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataRequest_AdyenParams.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataRequest_AdyenParams) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RetrieveSdkDataRequest_AdyenParams) GetSetupToken() string {
	if x != nil {
		return x.SetupToken
	}
	return ""
}

// adyen data
type RetrieveSdkDataResponse_AdyenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SDK data
	SdkData string `protobuf:"bytes,1,opt,name=sdk_data,json=sdkData,proto3" json:"sdk_data,omitempty"`
}

func (x *RetrieveSdkDataResponse_AdyenData) Reset() {
	*x = RetrieveSdkDataResponse_AdyenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveSdkDataResponse_AdyenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveSdkDataResponse_AdyenData) ProtoMessage() {}

func (x *RetrieveSdkDataResponse_AdyenData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveSdkDataResponse_AdyenData.ProtoReflect.Descriptor instead.
func (*RetrieveSdkDataResponse_AdyenData) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RetrieveSdkDataResponse_AdyenData) GetSdkData() string {
	if x != nil {
		return x.SdkData
	}
	return ""
}

var File_moego_service_payment_v2_payment_terminal_service_proto protoreflect.FileDescriptor

var file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xbe, 0x02, 0x0a, 0x16, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x0c, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x79, 0x65,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x64, 0x79, 0x65, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x0a, 0x0b, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x75,
	0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0xa7, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0a,
	0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x09, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x26, 0x0a, 0x09, 0x41, 0x64,
	0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x64, 0x6b, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x64, 0x6b, 0x44, 0x61,
	0x74, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x90, 0x01, 0x0a, 0x16, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x0f, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x53, 0x64, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x64,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x80, 0x01,
	0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDescOnce sync.Once
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData = file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc
)

func file_moego_service_payment_v2_payment_terminal_service_proto_rawDescGZIP() []byte {
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDescOnce.Do(func() {
		file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData)
	})
	return file_moego_service_payment_v2_payment_terminal_service_proto_rawDescData
}

var file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_payment_v2_payment_terminal_service_proto_goTypes = []interface{}{
	(*RetrieveSdkDataRequest)(nil),             // 0: moego.service.payment.v2.RetrieveSdkDataRequest
	(*RetrieveSdkDataResponse)(nil),            // 1: moego.service.payment.v2.RetrieveSdkDataResponse
	(*RetrieveSdkDataRequest_AdyenParams)(nil), // 2: moego.service.payment.v2.RetrieveSdkDataRequest.AdyenParams
	(*RetrieveSdkDataResponse_AdyenData)(nil),  // 3: moego.service.payment.v2.RetrieveSdkDataResponse.AdyenData
	(v2.ChannelType)(0),                        // 4: moego.models.payment.v2.ChannelType
}
var file_moego_service_payment_v2_payment_terminal_service_proto_depIdxs = []int32{
	4, // 0: moego.service.payment.v2.RetrieveSdkDataRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	2, // 1: moego.service.payment.v2.RetrieveSdkDataRequest.adyen_params:type_name -> moego.service.payment.v2.RetrieveSdkDataRequest.AdyenParams
	3, // 2: moego.service.payment.v2.RetrieveSdkDataResponse.adyen_data:type_name -> moego.service.payment.v2.RetrieveSdkDataResponse.AdyenData
	0, // 3: moego.service.payment.v2.PaymentTerminalService.RetrieveSdkData:input_type -> moego.service.payment.v2.RetrieveSdkDataRequest
	1, // 4: moego.service.payment.v2.PaymentTerminalService.RetrieveSdkData:output_type -> moego.service.payment.v2.RetrieveSdkDataResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_service_payment_v2_payment_terminal_service_proto_init() }
func file_moego_service_payment_v2_payment_terminal_service_proto_init() {
	if File_moego_service_payment_v2_payment_terminal_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataRequest_AdyenParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveSdkDataResponse_AdyenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*RetrieveSdkDataRequest_AdyenParams_)(nil),
	}
	file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*RetrieveSdkDataResponse_AdyenData_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_payment_v2_payment_terminal_service_proto_goTypes,
		DependencyIndexes: file_moego_service_payment_v2_payment_terminal_service_proto_depIdxs,
		MessageInfos:      file_moego_service_payment_v2_payment_terminal_service_proto_msgTypes,
	}.Build()
	File_moego_service_payment_v2_payment_terminal_service_proto = out.File
	file_moego_service_payment_v2_payment_terminal_service_proto_rawDesc = nil
	file_moego_service_payment_v2_payment_terminal_service_proto_goTypes = nil
	file_moego_service_payment_v2_payment_terminal_service_proto_depIdxs = nil
}
