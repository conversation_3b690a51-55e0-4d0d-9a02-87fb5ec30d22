// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/payment/v2/payment_service.proto

package paymentsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for GetPaymentVersion
type GetPaymentVersionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 收款方
	Payee *v2.User `protobuf:"bytes,1,opt,name=payee,proto3" json:"payee,omitempty"`
}

func (x *GetPaymentVersionRequest) Reset() {
	*x = GetPaymentVersionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentVersionRequest) ProtoMessage() {}

func (x *GetPaymentVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentVersionRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentVersionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetPaymentVersionRequest) GetPayee() *v2.User {
	if x != nil {
		return x.Payee
	}
	return nil
}

// Response for GetPaymentVersion
type GetPaymentVersionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付版本
	PaymentVersion v2.PaymentVersion `protobuf:"varint,1,opt,name=payment_version,json=paymentVersion,proto3,enum=moego.models.payment.v2.PaymentVersion" json:"payment_version,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetPaymentVersionResponse) Reset() {
	*x = GetPaymentVersionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentVersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentVersionResponse) ProtoMessage() {}

func (x *GetPaymentVersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentVersionResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentVersionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetPaymentVersionResponse) GetPaymentVersion() v2.PaymentVersion {
	if x != nil {
		return x.PaymentVersion
	}
	return v2.PaymentVersion(0)
}

func (x *GetPaymentVersionResponse) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Request for CreatePayment
type CreatePaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必填，上层业务系统类型
	ExternalType v2.ExternalType `protobuf:"varint,1,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 必填，上层业务系统内单据 ID
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 付款方，可选
	Payer *v2.User `protobuf:"bytes,3,opt,name=payer,proto3" json:"payer,omitempty"`
	// 收款方
	Payee *v2.User `protobuf:"bytes,4,opt,name=payee,proto3" json:"payee,omitempty"`
	// 金额
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付类型，决定是pre-pay、pre-auth还是常规支付
	PaymentType v2.PaymentModel_PaymentType `protobuf:"varint,6,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentType" json:"payment_type,omitempty"`
}

func (x *CreatePaymentRequest) Reset() {
	*x = CreatePaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentRequest) ProtoMessage() {}

func (x *CreatePaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentRequest.ProtoReflect.Descriptor instead.
func (*CreatePaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePaymentRequest) GetExternalType() v2.ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return v2.ExternalType(0)
}

func (x *CreatePaymentRequest) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *CreatePaymentRequest) GetPayer() *v2.User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *CreatePaymentRequest) GetPayee() *v2.User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *CreatePaymentRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CreatePaymentRequest) GetPaymentType() v2.PaymentModel_PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return v2.PaymentModel_PaymentType(0)
}

// Response for CreatePayment
type CreatePaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 生成的支付单据
	Payment *v2.PaymentModel `protobuf:"bytes,1,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *CreatePaymentResponse) Reset() {
	*x = CreatePaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentResponse) ProtoMessage() {}

func (x *CreatePaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentResponse.ProtoReflect.Descriptor instead.
func (*CreatePaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePaymentResponse) GetPayment() *v2.PaymentModel {
	if x != nil {
		return x.Payment
	}
	return nil
}

// cancel payment request
type CancelPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CancelPaymentRequest) Reset() {
	*x = CancelPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPaymentRequest) ProtoMessage() {}

func (x *CancelPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPaymentRequest.ProtoReflect.Descriptor instead.
func (*CancelPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{4}
}

func (x *CancelPaymentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// cancel payment response
type CancelPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg 可以展示给用户的信息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CancelPaymentResponse) Reset() {
	*x = CancelPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPaymentResponse) ProtoMessage() {}

func (x *CancelPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPaymentResponse.ProtoReflect.Descriptor instead.
func (*CancelPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{5}
}

func (x *CancelPaymentResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// Request for GetPayData
type GetPayDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道，可不传，将由后端根据渠道路由自行决定；如果传了，优先级高于后端路由
	ChannelType *v2.ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType,oneof" json:"channel_type,omitempty"`
}

func (x *GetPayDataRequest) Reset() {
	*x = GetPayDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataRequest) ProtoMessage() {}

func (x *GetPayDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataRequest.ProtoReflect.Descriptor instead.
func (*GetPayDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetPayDataRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetPayDataRequest) GetChannelType() v2.ChannelType {
	if x != nil && x.ChannelType != nil {
		return *x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetPayData
type GetPayDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 支付数据
	//
	// Types that are assignable to Data:
	//
	//	*GetPayDataResponse_AdyenData_
	Data isGetPayDataResponse_Data `protobuf_oneof:"data"`
}

func (x *GetPayDataResponse) Reset() {
	*x = GetPayDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataResponse) ProtoMessage() {}

func (x *GetPayDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataResponse.ProtoReflect.Descriptor instead.
func (*GetPayDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetPayDataResponse) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (m *GetPayDataResponse) GetData() isGetPayDataResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *GetPayDataResponse) GetAdyenData() *GetPayDataResponse_AdyenData {
	if x, ok := x.GetData().(*GetPayDataResponse_AdyenData_); ok {
		return x.AdyenData
	}
	return nil
}

type isGetPayDataResponse_Data interface {
	isGetPayDataResponse_Data()
}

type GetPayDataResponse_AdyenData_ struct {
	// adyen data
	AdyenData *GetPayDataResponse_AdyenData `protobuf:"bytes,2,opt,name=adyen_data,json=adyenData,proto3,oneof"`
}

func (*GetPayDataResponse_AdyenData_) isGetPayDataResponse_Data() {}

// Request for Pay
type PayPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据 id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 支付方式
	PaymentMethodType v2.PaymentMethod_MethodType `protobuf:"varint,2,opt,name=payment_method_type,json=paymentMethodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"payment_method_type,omitempty"`
	// 支付凭证
	Detail *v2.PaymentMethod_Detail `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail,omitempty"`
	// 是否添加cv fee,不传的时候后端判断
	AddConvenienceFee *bool `protobuf:"varint,4,opt,name=add_convenience_fee,json=addConvenienceFee,proto3,oneof" json:"add_convenience_fee,omitempty"`
	// payer, 一般是customer name
	Payer string `protobuf:"bytes,5,opt,name=payer,proto3" json:"payer,omitempty"`
	// payment description 支付描述 自定义
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *PayPaymentRequest) Reset() {
	*x = PayPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayPaymentRequest) ProtoMessage() {}

func (x *PayPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayPaymentRequest.ProtoReflect.Descriptor instead.
func (*PayPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{8}
}

func (x *PayPaymentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PayPaymentRequest) GetPaymentMethodType() v2.PaymentMethod_MethodType {
	if x != nil {
		return x.PaymentMethodType
	}
	return v2.PaymentMethod_MethodType(0)
}

func (x *PayPaymentRequest) GetDetail() *v2.PaymentMethod_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *PayPaymentRequest) GetAddConvenienceFee() bool {
	if x != nil && x.AddConvenienceFee != nil {
		return *x.AddConvenienceFee
	}
	return false
}

func (x *PayPaymentRequest) GetPayer() string {
	if x != nil {
		return x.Payer
	}
	return ""
}

func (x *PayPaymentRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// Response for Pay
type PayPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg 可以展示给用户的信息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// channel response，渠道返回的原始数据，用于前端加载第三方支付组件
	// e.g. adyen 3ds2:
	//
	//	`{
	//	  "resultCode": "IdentifyShopper",
	//	  "action": {
	//	    "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
	//	    "paymentMethodType": "scheme",
	//	    "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
	//	    "subtype": "fingerprint",
	//	    "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
	//	    "type": "threeDS2"
	//	  }
	//	}`
	ChannelResponse string `protobuf:"bytes,2,opt,name=channel_response,json=channelResponse,proto3" json:"channel_response,omitempty"`
}

func (x *PayPaymentResponse) Reset() {
	*x = PayPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayPaymentResponse) ProtoMessage() {}

func (x *PayPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayPaymentResponse.ProtoReflect.Descriptor instead.
func (*PayPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{9}
}

func (x *PayPaymentResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PayPaymentResponse) GetChannelResponse() string {
	if x != nil {
		return x.ChannelResponse
	}
	return ""
}

// Request for SubmitActionDetail
type SubmitActionDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据 id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Action Result，前端从组件拿到的原始数据，
	// e.g. ayden 3ds2:
	//
	//	`{
	//	  "details": {
	//	    "threeDSResult": "eyJ0cmFuc1N0YXR1cyI6IlkifQ=="
	//	  }
	//	}`
	ActionResult string `protobuf:"bytes,2,opt,name=action_result,json=actionResult,proto3" json:"action_result,omitempty"`
}

func (x *SubmitActionDetailRequest) Reset() {
	*x = SubmitActionDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitActionDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitActionDetailRequest) ProtoMessage() {}

func (x *SubmitActionDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitActionDetailRequest.ProtoReflect.Descriptor instead.
func (*SubmitActionDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{10}
}

func (x *SubmitActionDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubmitActionDetailRequest) GetActionResult() string {
	if x != nil {
		return x.ActionResult
	}
	return ""
}

// Response for SubmitPayDetail
type SubmitActionDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg 可以展示给用户的信息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// channel response，渠道返回的原始数据，用于前端加载第三方支付组件,
	// e.g. adyen:
	//
	//	`{
	//	  "resultCode": "Authorised",
	//	  "pspReference": "V4HZ4RBFJGXXGN82"
	//	}`
	ChannelResponse string `protobuf:"bytes,2,opt,name=channel_response,json=channelResponse,proto3" json:"channel_response,omitempty"`
}

func (x *SubmitActionDetailResponse) Reset() {
	*x = SubmitActionDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitActionDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitActionDetailResponse) ProtoMessage() {}

func (x *SubmitActionDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitActionDetailResponse.ProtoReflect.Descriptor instead.
func (*SubmitActionDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{11}
}

func (x *SubmitActionDetailResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SubmitActionDetailResponse) GetChannelResponse() string {
	if x != nil {
		return x.ChannelResponse
	}
	return ""
}

// get payment request
type GetPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPaymentRequest) Reset() {
	*x = GetPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentRequest) ProtoMessage() {}

func (x *GetPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetPaymentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get payment response
type GetPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment 实体
	Payment *v2.PaymentModel `protobuf:"bytes,1,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *GetPaymentResponse) Reset() {
	*x = GetPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentResponse) ProtoMessage() {}

func (x *GetPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetPaymentResponse) GetPayment() *v2.PaymentModel {
	if x != nil {
		return x.Payment
	}
	return nil
}

// list payment request
type ListPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页查询请求
	PaginationRequest *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// filter
	Filter *ListPaymentRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPaymentRequest) Reset() {
	*x = ListPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentRequest) ProtoMessage() {}

func (x *ListPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentRequest.ProtoReflect.Descriptor instead.
func (*ListPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListPaymentRequest) GetPaginationRequest() *v21.PaginationRequest {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListPaymentRequest) GetFilter() *ListPaymentRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list payment response
type ListPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付列表
	PaymentViews []*v2.PaymentView `protobuf:"bytes,1,rep,name=payment_views,json=paymentViews,proto3" json:"payment_views,omitempty"`
	// 分页
	PaginationRequest *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
}

func (x *ListPaymentResponse) Reset() {
	*x = ListPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentResponse) ProtoMessage() {}

func (x *ListPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentResponse.ProtoReflect.Descriptor instead.
func (*ListPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListPaymentResponse) GetPaymentViews() []*v2.PaymentView {
	if x != nil {
		return x.PaymentViews
	}
	return nil
}

func (x *ListPaymentResponse) GetPaginationRequest() *v21.PaginationResponse {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

// add recurring payment method request
type AddRecurringPaymentMethodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId *int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// customer code
	EncryptedCustomerId *string `protobuf:"bytes,2,opt,name=encrypted_customer_id,json=encryptedCustomerId,proto3,oneof" json:"encrypted_customer_id,omitempty"`
	// channel type
	ChannelType *v2.ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType,oneof" json:"channel_type,omitempty"`
	// payment method type
	PaymentMethodType v2.PaymentMethod_MethodType `protobuf:"varint,4,opt,name=payment_method_type,json=paymentMethodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"payment_method_type,omitempty"`
	// 支付凭证
	Detail *v2.PaymentMethod_Detail `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`
	// 透传参数，一般是用户自定义的额外信息
	Extra *v2.RecurringPaymentMethodModel_Extra `protobuf:"bytes,6,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
}

func (x *AddRecurringPaymentMethodRequest) Reset() {
	*x = AddRecurringPaymentMethodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRecurringPaymentMethodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRecurringPaymentMethodRequest) ProtoMessage() {}

func (x *AddRecurringPaymentMethodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRecurringPaymentMethodRequest.ProtoReflect.Descriptor instead.
func (*AddRecurringPaymentMethodRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{16}
}

func (x *AddRecurringPaymentMethodRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *AddRecurringPaymentMethodRequest) GetEncryptedCustomerId() string {
	if x != nil && x.EncryptedCustomerId != nil {
		return *x.EncryptedCustomerId
	}
	return ""
}

func (x *AddRecurringPaymentMethodRequest) GetChannelType() v2.ChannelType {
	if x != nil && x.ChannelType != nil {
		return *x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *AddRecurringPaymentMethodRequest) GetPaymentMethodType() v2.PaymentMethod_MethodType {
	if x != nil {
		return x.PaymentMethodType
	}
	return v2.PaymentMethod_MethodType(0)
}

func (x *AddRecurringPaymentMethodRequest) GetDetail() *v2.PaymentMethod_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *AddRecurringPaymentMethodRequest) GetExtra() *v2.RecurringPaymentMethodModel_Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

// add recurring payment method response
type AddRecurringPaymentMethodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已保存的支付方式
	RecurringPaymentMethod *v2.RecurringPaymentMethodModel `protobuf:"bytes,1,opt,name=recurring_payment_method,json=recurringPaymentMethod,proto3" json:"recurring_payment_method,omitempty"`
}

func (x *AddRecurringPaymentMethodResponse) Reset() {
	*x = AddRecurringPaymentMethodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRecurringPaymentMethodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRecurringPaymentMethodResponse) ProtoMessage() {}

func (x *AddRecurringPaymentMethodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRecurringPaymentMethodResponse.ProtoReflect.Descriptor instead.
func (*AddRecurringPaymentMethodResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{17}
}

func (x *AddRecurringPaymentMethodResponse) GetRecurringPaymentMethod() *v2.RecurringPaymentMethodModel {
	if x != nil {
		return x.RecurringPaymentMethod
	}
	return nil
}

// delete recurring payment method request
type DeleteRecurringPaymentMethodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 存储的 payment method id
	PaymentMethodId int64 `protobuf:"varint,1,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *DeleteRecurringPaymentMethodRequest) Reset() {
	*x = DeleteRecurringPaymentMethodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecurringPaymentMethodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecurringPaymentMethodRequest) ProtoMessage() {}

func (x *DeleteRecurringPaymentMethodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecurringPaymentMethodRequest.ProtoReflect.Descriptor instead.
func (*DeleteRecurringPaymentMethodRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteRecurringPaymentMethodRequest) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

// delete recurring payment method response
type DeleteRecurringPaymentMethodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRecurringPaymentMethodResponse) Reset() {
	*x = DeleteRecurringPaymentMethodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecurringPaymentMethodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecurringPaymentMethodResponse) ProtoMessage() {}

func (x *DeleteRecurringPaymentMethodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecurringPaymentMethodResponse.ProtoReflect.Descriptor instead.
func (*DeleteRecurringPaymentMethodResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{19}
}

// set recurring payment method primary request
type SetRecurringPaymentMethodPrimaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment method id
	PaymentMethodId int64 `protobuf:"varint,1,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *SetRecurringPaymentMethodPrimaryRequest) Reset() {
	*x = SetRecurringPaymentMethodPrimaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecurringPaymentMethodPrimaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecurringPaymentMethodPrimaryRequest) ProtoMessage() {}

func (x *SetRecurringPaymentMethodPrimaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecurringPaymentMethodPrimaryRequest.ProtoReflect.Descriptor instead.
func (*SetRecurringPaymentMethodPrimaryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{20}
}

func (x *SetRecurringPaymentMethodPrimaryRequest) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

// set recurring payment method primary response
type SetRecurringPaymentMethodPrimaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已设置的 payment method
	RecurringPaymentMethod *v2.RecurringPaymentMethodModel `protobuf:"bytes,1,opt,name=recurring_payment_method,json=recurringPaymentMethod,proto3" json:"recurring_payment_method,omitempty"`
}

func (x *SetRecurringPaymentMethodPrimaryResponse) Reset() {
	*x = SetRecurringPaymentMethodPrimaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecurringPaymentMethodPrimaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecurringPaymentMethodPrimaryResponse) ProtoMessage() {}

func (x *SetRecurringPaymentMethodPrimaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecurringPaymentMethodPrimaryResponse.ProtoReflect.Descriptor instead.
func (*SetRecurringPaymentMethodPrimaryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{21}
}

func (x *SetRecurringPaymentMethodPrimaryResponse) GetRecurringPaymentMethod() *v2.RecurringPaymentMethodModel {
	if x != nil {
		return x.RecurringPaymentMethod
	}
	return nil
}

// list recurring payment method request
type ListRecurringPaymentMethodsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页请求
	PaginationRequest *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// filter
	Filter *ListRecurringPaymentMethodsRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListRecurringPaymentMethodsRequest) Reset() {
	*x = ListRecurringPaymentMethodsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsRequest) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsRequest.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{22}
}

func (x *ListRecurringPaymentMethodsRequest) GetPaginationRequest() *v21.PaginationRequest {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListRecurringPaymentMethodsRequest) GetFilter() *ListRecurringPaymentMethodsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list recurring payment method response
type ListRecurringPaymentMethodsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页
	PaginationResponse *v21.PaginationResponse `protobuf:"bytes,1,opt,name=pagination_response,json=paginationResponse,proto3" json:"pagination_response,omitempty"`
	// 已保存的支付方式
	PaymentMethods []*v2.RecurringPaymentMethodModel `protobuf:"bytes,2,rep,name=payment_methods,json=paymentMethods,proto3" json:"payment_methods,omitempty"`
}

func (x *ListRecurringPaymentMethodsResponse) Reset() {
	*x = ListRecurringPaymentMethodsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsResponse) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsResponse.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{23}
}

func (x *ListRecurringPaymentMethodsResponse) GetPaginationResponse() *v21.PaginationResponse {
	if x != nil {
		return x.PaginationResponse
	}
	return nil
}

func (x *ListRecurringPaymentMethodsResponse) GetPaymentMethods() []*v2.RecurringPaymentMethodModel {
	if x != nil {
		return x.PaymentMethods
	}
	return nil
}

// adyen data
type GetPayDataResponse_AdyenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data
	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetPayDataResponse_AdyenData) Reset() {
	*x = GetPayDataResponse_AdyenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataResponse_AdyenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataResponse_AdyenData) ProtoMessage() {}

func (x *GetPayDataResponse_AdyenData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataResponse_AdyenData.ProtoReflect.Descriptor instead.
func (*GetPayDataResponse_AdyenData) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetPayDataResponse_AdyenData) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// filter
type ListPaymentRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 买家
	Payers []*v2.User `protobuf:"bytes,1,rep,name=payers,proto3" json:"payers,omitempty"`
	// 卖家
	Payees []*v2.User `protobuf:"bytes,2,rep,name=payees,proto3" json:"payees,omitempty"`
	// order id
	OrderIds []int64 `protobuf:"varint,3,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// order payment id
	OrderPaymentIds []int64 `protobuf:"varint,4,rep,packed,name=order_payment_ids,json=orderPaymentIds,proto3" json:"order_payment_ids,omitempty"`
	// payment
	Ids []int64 `protobuf:"varint,5,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 查询时间范围
	TimePeriod *v1.TimePeriod `protobuf:"bytes,6,opt,name=time_period,json=timePeriod,proto3" json:"time_period,omitempty"`
}

func (x *ListPaymentRequest_Filter) Reset() {
	*x = ListPaymentRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentRequest_Filter) ProtoMessage() {}

func (x *ListPaymentRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPaymentRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListPaymentRequest_Filter) GetPayers() []*v2.User {
	if x != nil {
		return x.Payers
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetPayees() []*v2.User {
	if x != nil {
		return x.Payees
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetOrderPaymentIds() []int64 {
	if x != nil {
		return x.OrderPaymentIds
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetTimePeriod() *v1.TimePeriod {
	if x != nil {
		return x.TimePeriod
	}
	return nil
}

// filter
type ListRecurringPaymentMethodsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户
	Users []*v2.User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// payment method ids
	RecurringPaymentMethodIds []int64 `protobuf:"varint,2,rep,packed,name=recurring_payment_method_ids,json=recurringPaymentMethodIds,proto3" json:"recurring_payment_method_ids,omitempty"` // users
}

func (x *ListRecurringPaymentMethodsRequest_Filter) Reset() {
	*x = ListRecurringPaymentMethodsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsRequest_Filter) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{22, 0}
}

func (x *ListRecurringPaymentMethodsRequest_Filter) GetUsers() []*v2.User {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *ListRecurringPaymentMethodsRequest_Filter) GetRecurringPaymentMethodIds() []int64 {
	if x != nil {
		return x.RecurringPaymentMethodIds
	}
	return nil
}

var File_moego_service_payment_v2_payment_service_proto protoreflect.FileDescriptor

var file_moego_service_payment_v2_payment_service_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xef, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x70,
	0x61, 0x79, 0x65, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x0c,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x58, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x26, 0x0a, 0x14,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x29, 0x0a, 0x15, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x93, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xdf, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x0a, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x09, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x1f,
	0x0a, 0x09, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42,
	0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd2, 0x02, 0x0a, 0x11, 0x50, 0x61, 0x79, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x61, 0x0a,
	0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x45, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a, 0x13, 0x61, 0x64, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x11, 0x61, 0x64, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79,
	0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x22, 0x51, 0x0a, 0x12,
	0x50, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x50, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x59, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x55, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xc4, 0x03, 0x0a, 0x12, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x50, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x4b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x8e,
	0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x06, 0x70, 0x61, 0x79,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x06, 0x70, 0x61, 0x79, 0x65, 0x72, 0x73,
	0x12, 0x35, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x65, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x06, 0x70, 0x61, 0x79, 0x65, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22,
	0xb3, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x95, 0x04, 0x0a, 0x20, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x13, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4c, 0x0a, 0x0c, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x06, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x55, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x03, 0x52, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x93, 0x01,
	0x0a, 0x21, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x18, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x16, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x22, 0x51, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x22, 0x26, 0x0a, 0x24, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x55,
	0x0a, 0x27, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x49, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x28, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x6e, 0x0a, 0x18, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x16, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x22, 0xd3, 0x02, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x12, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x7e, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x19, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x73, 0x22, 0xd9, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x53, 0x0a, 0x13, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x73, 0x32, 0x9a, 0x0c, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x50, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x12, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x94, 0x01, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa9, 0x01, 0x0a, 0x20, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x41, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_payment_v2_payment_service_proto_rawDescOnce sync.Once
	file_moego_service_payment_v2_payment_service_proto_rawDescData = file_moego_service_payment_v2_payment_service_proto_rawDesc
)

func file_moego_service_payment_v2_payment_service_proto_rawDescGZIP() []byte {
	file_moego_service_payment_v2_payment_service_proto_rawDescOnce.Do(func() {
		file_moego_service_payment_v2_payment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_payment_v2_payment_service_proto_rawDescData)
	})
	return file_moego_service_payment_v2_payment_service_proto_rawDescData
}

var file_moego_service_payment_v2_payment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_moego_service_payment_v2_payment_service_proto_goTypes = []interface{}{
	(*GetPaymentVersionRequest)(nil),                  // 0: moego.service.payment.v2.GetPaymentVersionRequest
	(*GetPaymentVersionResponse)(nil),                 // 1: moego.service.payment.v2.GetPaymentVersionResponse
	(*CreatePaymentRequest)(nil),                      // 2: moego.service.payment.v2.CreatePaymentRequest
	(*CreatePaymentResponse)(nil),                     // 3: moego.service.payment.v2.CreatePaymentResponse
	(*CancelPaymentRequest)(nil),                      // 4: moego.service.payment.v2.CancelPaymentRequest
	(*CancelPaymentResponse)(nil),                     // 5: moego.service.payment.v2.CancelPaymentResponse
	(*GetPayDataRequest)(nil),                         // 6: moego.service.payment.v2.GetPayDataRequest
	(*GetPayDataResponse)(nil),                        // 7: moego.service.payment.v2.GetPayDataResponse
	(*PayPaymentRequest)(nil),                         // 8: moego.service.payment.v2.PayPaymentRequest
	(*PayPaymentResponse)(nil),                        // 9: moego.service.payment.v2.PayPaymentResponse
	(*SubmitActionDetailRequest)(nil),                 // 10: moego.service.payment.v2.SubmitActionDetailRequest
	(*SubmitActionDetailResponse)(nil),                // 11: moego.service.payment.v2.SubmitActionDetailResponse
	(*GetPaymentRequest)(nil),                         // 12: moego.service.payment.v2.GetPaymentRequest
	(*GetPaymentResponse)(nil),                        // 13: moego.service.payment.v2.GetPaymentResponse
	(*ListPaymentRequest)(nil),                        // 14: moego.service.payment.v2.ListPaymentRequest
	(*ListPaymentResponse)(nil),                       // 15: moego.service.payment.v2.ListPaymentResponse
	(*AddRecurringPaymentMethodRequest)(nil),          // 16: moego.service.payment.v2.AddRecurringPaymentMethodRequest
	(*AddRecurringPaymentMethodResponse)(nil),         // 17: moego.service.payment.v2.AddRecurringPaymentMethodResponse
	(*DeleteRecurringPaymentMethodRequest)(nil),       // 18: moego.service.payment.v2.DeleteRecurringPaymentMethodRequest
	(*DeleteRecurringPaymentMethodResponse)(nil),      // 19: moego.service.payment.v2.DeleteRecurringPaymentMethodResponse
	(*SetRecurringPaymentMethodPrimaryRequest)(nil),   // 20: moego.service.payment.v2.SetRecurringPaymentMethodPrimaryRequest
	(*SetRecurringPaymentMethodPrimaryResponse)(nil),  // 21: moego.service.payment.v2.SetRecurringPaymentMethodPrimaryResponse
	(*ListRecurringPaymentMethodsRequest)(nil),        // 22: moego.service.payment.v2.ListRecurringPaymentMethodsRequest
	(*ListRecurringPaymentMethodsResponse)(nil),       // 23: moego.service.payment.v2.ListRecurringPaymentMethodsResponse
	(*GetPayDataResponse_AdyenData)(nil),              // 24: moego.service.payment.v2.GetPayDataResponse.AdyenData
	(*ListPaymentRequest_Filter)(nil),                 // 25: moego.service.payment.v2.ListPaymentRequest.Filter
	(*ListRecurringPaymentMethodsRequest_Filter)(nil), // 26: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.Filter
	(*v2.User)(nil),                                   // 27: moego.models.payment.v2.User
	(v2.PaymentVersion)(0),                            // 28: moego.models.payment.v2.PaymentVersion
	(v2.ChannelType)(0),                               // 29: moego.models.payment.v2.ChannelType
	(v2.ExternalType)(0),                              // 30: moego.models.payment.v2.ExternalType
	(*money.Money)(nil),                               // 31: google.type.Money
	(v2.PaymentModel_PaymentType)(0),                  // 32: moego.models.payment.v2.PaymentModel.PaymentType
	(*v2.PaymentModel)(nil),                           // 33: moego.models.payment.v2.PaymentModel
	(v2.PaymentMethod_MethodType)(0),                  // 34: moego.models.payment.v2.PaymentMethod.MethodType
	(*v2.PaymentMethod_Detail)(nil),                   // 35: moego.models.payment.v2.PaymentMethod.Detail
	(*v21.PaginationRequest)(nil),                     // 36: moego.utils.v2.PaginationRequest
	(*v2.PaymentView)(nil),                            // 37: moego.models.payment.v2.PaymentView
	(*v21.PaginationResponse)(nil),                    // 38: moego.utils.v2.PaginationResponse
	(*v2.RecurringPaymentMethodModel_Extra)(nil),      // 39: moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	(*v2.RecurringPaymentMethodModel)(nil),            // 40: moego.models.payment.v2.RecurringPaymentMethodModel
	(*v1.TimePeriod)(nil),                             // 41: moego.utils.v1.TimePeriod
}
var file_moego_service_payment_v2_payment_service_proto_depIdxs = []int32{
	27, // 0: moego.service.payment.v2.GetPaymentVersionRequest.payee:type_name -> moego.models.payment.v2.User
	28, // 1: moego.service.payment.v2.GetPaymentVersionResponse.payment_version:type_name -> moego.models.payment.v2.PaymentVersion
	29, // 2: moego.service.payment.v2.GetPaymentVersionResponse.channel_type:type_name -> moego.models.payment.v2.ChannelType
	30, // 3: moego.service.payment.v2.CreatePaymentRequest.external_type:type_name -> moego.models.payment.v2.ExternalType
	27, // 4: moego.service.payment.v2.CreatePaymentRequest.payer:type_name -> moego.models.payment.v2.User
	27, // 5: moego.service.payment.v2.CreatePaymentRequest.payee:type_name -> moego.models.payment.v2.User
	31, // 6: moego.service.payment.v2.CreatePaymentRequest.amount:type_name -> google.type.Money
	32, // 7: moego.service.payment.v2.CreatePaymentRequest.payment_type:type_name -> moego.models.payment.v2.PaymentModel.PaymentType
	33, // 8: moego.service.payment.v2.CreatePaymentResponse.payment:type_name -> moego.models.payment.v2.PaymentModel
	29, // 9: moego.service.payment.v2.GetPayDataRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	29, // 10: moego.service.payment.v2.GetPayDataResponse.channel_type:type_name -> moego.models.payment.v2.ChannelType
	24, // 11: moego.service.payment.v2.GetPayDataResponse.adyen_data:type_name -> moego.service.payment.v2.GetPayDataResponse.AdyenData
	34, // 12: moego.service.payment.v2.PayPaymentRequest.payment_method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	35, // 13: moego.service.payment.v2.PayPaymentRequest.detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	33, // 14: moego.service.payment.v2.GetPaymentResponse.payment:type_name -> moego.models.payment.v2.PaymentModel
	36, // 15: moego.service.payment.v2.ListPaymentRequest.pagination_request:type_name -> moego.utils.v2.PaginationRequest
	25, // 16: moego.service.payment.v2.ListPaymentRequest.filter:type_name -> moego.service.payment.v2.ListPaymentRequest.Filter
	37, // 17: moego.service.payment.v2.ListPaymentResponse.payment_views:type_name -> moego.models.payment.v2.PaymentView
	38, // 18: moego.service.payment.v2.ListPaymentResponse.pagination_request:type_name -> moego.utils.v2.PaginationResponse
	29, // 19: moego.service.payment.v2.AddRecurringPaymentMethodRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	34, // 20: moego.service.payment.v2.AddRecurringPaymentMethodRequest.payment_method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	35, // 21: moego.service.payment.v2.AddRecurringPaymentMethodRequest.detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	39, // 22: moego.service.payment.v2.AddRecurringPaymentMethodRequest.extra:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	40, // 23: moego.service.payment.v2.AddRecurringPaymentMethodResponse.recurring_payment_method:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel
	40, // 24: moego.service.payment.v2.SetRecurringPaymentMethodPrimaryResponse.recurring_payment_method:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel
	36, // 25: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.pagination_request:type_name -> moego.utils.v2.PaginationRequest
	26, // 26: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.filter:type_name -> moego.service.payment.v2.ListRecurringPaymentMethodsRequest.Filter
	38, // 27: moego.service.payment.v2.ListRecurringPaymentMethodsResponse.pagination_response:type_name -> moego.utils.v2.PaginationResponse
	40, // 28: moego.service.payment.v2.ListRecurringPaymentMethodsResponse.payment_methods:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel
	27, // 29: moego.service.payment.v2.ListPaymentRequest.Filter.payers:type_name -> moego.models.payment.v2.User
	27, // 30: moego.service.payment.v2.ListPaymentRequest.Filter.payees:type_name -> moego.models.payment.v2.User
	41, // 31: moego.service.payment.v2.ListPaymentRequest.Filter.time_period:type_name -> moego.utils.v1.TimePeriod
	27, // 32: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.Filter.users:type_name -> moego.models.payment.v2.User
	0,  // 33: moego.service.payment.v2.PaymentService.GetPaymentVersion:input_type -> moego.service.payment.v2.GetPaymentVersionRequest
	2,  // 34: moego.service.payment.v2.PaymentService.CreatePayment:input_type -> moego.service.payment.v2.CreatePaymentRequest
	4,  // 35: moego.service.payment.v2.PaymentService.CancelPayment:input_type -> moego.service.payment.v2.CancelPaymentRequest
	6,  // 36: moego.service.payment.v2.PaymentService.GetPayData:input_type -> moego.service.payment.v2.GetPayDataRequest
	8,  // 37: moego.service.payment.v2.PaymentService.PayPayment:input_type -> moego.service.payment.v2.PayPaymentRequest
	10, // 38: moego.service.payment.v2.PaymentService.SubmitActionDetail:input_type -> moego.service.payment.v2.SubmitActionDetailRequest
	12, // 39: moego.service.payment.v2.PaymentService.GetPayment:input_type -> moego.service.payment.v2.GetPaymentRequest
	14, // 40: moego.service.payment.v2.PaymentService.ListPayment:input_type -> moego.service.payment.v2.ListPaymentRequest
	16, // 41: moego.service.payment.v2.PaymentService.AddRecurringPaymentMethod:input_type -> moego.service.payment.v2.AddRecurringPaymentMethodRequest
	18, // 42: moego.service.payment.v2.PaymentService.DeleteRecurringPaymentMethod:input_type -> moego.service.payment.v2.DeleteRecurringPaymentMethodRequest
	20, // 43: moego.service.payment.v2.PaymentService.SetRecurringPaymentMethodPrimary:input_type -> moego.service.payment.v2.SetRecurringPaymentMethodPrimaryRequest
	22, // 44: moego.service.payment.v2.PaymentService.ListRecurringPaymentMethods:input_type -> moego.service.payment.v2.ListRecurringPaymentMethodsRequest
	1,  // 45: moego.service.payment.v2.PaymentService.GetPaymentVersion:output_type -> moego.service.payment.v2.GetPaymentVersionResponse
	3,  // 46: moego.service.payment.v2.PaymentService.CreatePayment:output_type -> moego.service.payment.v2.CreatePaymentResponse
	5,  // 47: moego.service.payment.v2.PaymentService.CancelPayment:output_type -> moego.service.payment.v2.CancelPaymentResponse
	7,  // 48: moego.service.payment.v2.PaymentService.GetPayData:output_type -> moego.service.payment.v2.GetPayDataResponse
	9,  // 49: moego.service.payment.v2.PaymentService.PayPayment:output_type -> moego.service.payment.v2.PayPaymentResponse
	11, // 50: moego.service.payment.v2.PaymentService.SubmitActionDetail:output_type -> moego.service.payment.v2.SubmitActionDetailResponse
	13, // 51: moego.service.payment.v2.PaymentService.GetPayment:output_type -> moego.service.payment.v2.GetPaymentResponse
	15, // 52: moego.service.payment.v2.PaymentService.ListPayment:output_type -> moego.service.payment.v2.ListPaymentResponse
	17, // 53: moego.service.payment.v2.PaymentService.AddRecurringPaymentMethod:output_type -> moego.service.payment.v2.AddRecurringPaymentMethodResponse
	19, // 54: moego.service.payment.v2.PaymentService.DeleteRecurringPaymentMethod:output_type -> moego.service.payment.v2.DeleteRecurringPaymentMethodResponse
	21, // 55: moego.service.payment.v2.PaymentService.SetRecurringPaymentMethodPrimary:output_type -> moego.service.payment.v2.SetRecurringPaymentMethodPrimaryResponse
	23, // 56: moego.service.payment.v2.PaymentService.ListRecurringPaymentMethods:output_type -> moego.service.payment.v2.ListRecurringPaymentMethodsResponse
	45, // [45:57] is the sub-list for method output_type
	33, // [33:45] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_moego_service_payment_v2_payment_service_proto_init() }
func file_moego_service_payment_v2_payment_service_proto_init() {
	if File_moego_service_payment_v2_payment_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_payment_v2_payment_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentVersionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentVersionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitActionDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitActionDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRecurringPaymentMethodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRecurringPaymentMethodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecurringPaymentMethodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecurringPaymentMethodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecurringPaymentMethodPrimaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecurringPaymentMethodPrimaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataResponse_AdyenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*GetPayDataResponse_AdyenData_)(nil),
	}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_payment_v2_payment_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_payment_v2_payment_service_proto_goTypes,
		DependencyIndexes: file_moego_service_payment_v2_payment_service_proto_depIdxs,
		MessageInfos:      file_moego_service_payment_v2_payment_service_proto_msgTypes,
	}.Build()
	File_moego_service_payment_v2_payment_service_proto = out.File
	file_moego_service_payment_v2_payment_service_proto_rawDesc = nil
	file_moego_service_payment_v2_payment_service_proto_goTypes = nil
	file_moego_service_payment_v2_payment_service_proto_depIdxs = nil
}
