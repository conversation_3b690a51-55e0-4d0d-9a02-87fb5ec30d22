package com.moego.svc.organization.utils;

import com.moego.idl.models.organization.v1.BookingLimitation;
import com.moego.idl.models.organization.v1.BookingLimitationDef;
import com.moego.idl.models.organization.v1.LimitType;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.svc.organization.entity.DayHourLimit;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.experimental.UtilityClass;

@UtilityClass
public class AvailabilityDayHourUtils {

    public static Map<String, SlotAvailabilityDay> getSlotDayByStartEndTime(
            LocalDate slotStartSunday,
            String inputStartDate,
            ScheduleType scheduleType,
            List<SlotAvailabilityDay> slotDays) {
        // 对传入的slotDays，先按ScheduleType，再按DayOfWeek排序
        slotDays.sort((o1, o2) -> {
            if (o1.getScheduleType() != o2.getScheduleType()) {
                return o1.getScheduleType().getNumber() - o2.getScheduleType().getNumber();
            }
            var o1DayOfWeek = o1.getDayOfWeek().getNumber();
            if (o1DayOfWeek == 7) {
                o1DayOfWeek = 0;
            }
            var o2DayOfWeek = o2.getDayOfWeek().getNumber();
            if (o2DayOfWeek == 7) {
                o2DayOfWeek = 0;
            }
            return o1DayOfWeek - o2DayOfWeek;
        });

        // 计算 inputStartDate 所在星期的星期日，也就是绝对开始时间
        LocalDate absoluteStartDate =
                LocalDate.parse(inputStartDate).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));

        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(slotStartSunday, absoluteStartDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, 7);
        // 计算循环中第几周，0 是第一周，1 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType.getNumber()));
        int startIndex = weekNumber * 7;
        int endIndex = (weekNumber + 1) * 7;
        // 返回slotDays的这一周的数据
        var days = slotDays.subList(startIndex, endIndex);
        // 以absoluteStartDate为第一天，将days作为周数据返回
        Map<String, SlotAvailabilityDay> result = new HashMap<>();
        for (int i = 0; i < days.size(); i++) {
            var day = days.get(i);
            var dayKey = absoluteStartDate.plusDays(i);
            result.put(dayKey.toString(), day);
        }
        return result;
    }

    public static Map<String, TimeAvailabilityDay> getTimeDayByStartEndTime(
            LocalDate timeStartSunday,
            String inputStartDate,
            ScheduleType scheduleType,
            List<TimeAvailabilityDay> timeDays) {
        // 对传入的 timeDays，先按 ScheduleType，再按 DayOfWeek 排序
        timeDays.sort((o1, o2) -> {
            if (o1.getScheduleType() != o2.getScheduleType()) {
                return o1.getScheduleType().getNumber() - o2.getScheduleType().getNumber();
            }
            var o1DayOfWeek = o1.getDayOfWeek().getNumber();
            if (o1DayOfWeek == 7) {
                o1DayOfWeek = 0;
            }
            var o2DayOfWeek = o2.getDayOfWeek().getNumber();
            if (o2DayOfWeek == 7) {
                o2DayOfWeek = 0;
            }
            return o1DayOfWeek - o2DayOfWeek;
        });

        // 计算 inputStartDate 所在星期的星期日，也就是绝对开始时间
        LocalDate absoluteStartDate =
                LocalDate.parse(inputStartDate).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));

        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(timeStartSunday, absoluteStartDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, 7);
        // 计算循环中第几周，0 是第一周，1 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType.getNumber()));
        int startIndex = weekNumber * 7;
        int endIndex = (weekNumber + 1) * 7;
        // 返回slotDays的这一周的数据
        var days = timeDays.subList(startIndex, endIndex);
        // 以absoluteStartDate为第一天，将days作为周数据返回
        Map<String, TimeAvailabilityDay> result = new HashMap<>();
        for (int i = 0; i < days.size(); i++) {
            var day = days.get(i);
            var dayKey = absoluteStartDate.plusDays(i);
            result.put(dayKey.toString(), day);
        }
        return result;
    }

    public static String getStaffAvailabilityDayKey(
            Long businessId, Long staffId, Integer scheduleType, Integer dayOfWeek) {
        return businessId + "_" + staffId + "_" + scheduleType + "_" + dayOfWeek;
    }

    public static BookingLimitation buildBookingLimitationPBModel(List<DayHourLimit> limits) {
        BookingLimitation.Builder builder = BookingLimitation.newBuilder();
        limits.forEach(limit -> {
            switch (limit.getType()) {
                case LimitType.SERVICE_LIMIT_VALUE:
                    builder.addServiceLimits(BookingLimitation.ServiceLimitation.newBuilder()
                            .addAllServiceIds(limit.getServiceIds())
                            .setCapacity(limit.getCapacity())
                            .setIsAllService(limit.getIsAllService())
                            .build());
                    break;
                case LimitType.PET_SIZE_LIMIT_VALUE:
                    builder.addPetSizeLimits(BookingLimitation.PetSizeLimitation.newBuilder()
                            .addAllPetSizeIds(limit.getPetSizeIds())
                            .setCapacity(limit.getCapacity())
                            .build());
                    break;
                case LimitType.PET_BREED_LIMIT_VALUE:
                    builder.addPetBreedLimits(BookingLimitation.PetBreedLimitation.newBuilder()
                            .addAllBreedIds(limit.getBreedIds())
                            .setCapacity(limit.getCapacity())
                            .setPetTypeId(limit.getPetTypeId())
                            .setIsAllBreed(limit.getIsAllBreed())
                            .build());
                    break;
                default:
                    break;
            }
        });
        return builder.build();
    }

    public static List<DayHourLimit> buildDayHourLimits(BookingLimitation limitDef) {
        List<DayHourLimit> limits = new ArrayList<>();
        limitDef.getServiceLimitsList().forEach(petSizeLimit -> {
            DayHourLimit limit = DayHourLimit.builder().build();
            limit.setType(LimitType.SERVICE_LIMIT_VALUE);
            limit.setServiceIds(petSizeLimit.getServiceIdsList());
            limit.setIsAllService(petSizeLimit.getIsAllService());
            limit.setCapacity(petSizeLimit.getCapacity());
            limit.setCreatedAt(LocalDateTime.now());
            limit.setUpdatedAt(LocalDateTime.now());
            limits.add(limit);
        });
        limitDef.getPetSizeLimitsList().forEach(petSizeLimit -> {
            DayHourLimit limit = DayHourLimit.builder().build();
            limit.setType(LimitType.PET_SIZE_LIMIT_VALUE);
            limit.setPetSizeIds(petSizeLimit.getPetSizeIdsList());
            limit.setCapacity(petSizeLimit.getCapacity());
            limit.setCreatedAt(LocalDateTime.now());
            limit.setUpdatedAt(LocalDateTime.now());
            limits.add(limit);
        });
        limitDef.getPetBreedLimitsList().forEach(petBreedLimit -> {
            DayHourLimit limit = DayHourLimit.builder().build();
            limit.setType(LimitType.PET_BREED_LIMIT_VALUE);
            limit.setBreedIds(petBreedLimit.getBreedIdsList());
            limit.setCapacity(petBreedLimit.getCapacity());
            limit.setPetTypeId(petBreedLimit.getPetTypeId());
            limit.setIsAllBreed(petBreedLimit.getIsAllBreed());
            limit.setCreatedAt(LocalDateTime.now());
            limit.setUpdatedAt(LocalDateTime.now());
            limits.add(limit);
        });
        return limits;
    }

    public static List<DayHourLimit> buildDayHourLimitsByDef(BookingLimitationDef limitDef) {
        List<DayHourLimit> limits = new ArrayList<>();
        limitDef.getServiceLimitsList().forEach(serviceLimit -> {
            DayHourLimit limit = DayHourLimit.builder().build();
            limit.setType(LimitType.SERVICE_LIMIT_VALUE);
            limit.setServiceIds(serviceLimit.getServiceIdsList());
            limit.setIsAllService(serviceLimit.getIsAllService());
            limit.setCapacity(serviceLimit.getCapacity());
            limit.setCreatedAt(LocalDateTime.now());
            limit.setUpdatedAt(LocalDateTime.now());
            limits.add(limit);
        });
        limitDef.getPetSizeLimitsList().forEach(petSizeLimit -> {
            DayHourLimit limit = DayHourLimit.builder().build();
            limit.setType(LimitType.PET_SIZE_LIMIT_VALUE);
            limit.setPetSizeIds(petSizeLimit.getPetSizeIdsList());
            limit.setCapacity(petSizeLimit.getCapacity());
            limit.setCreatedAt(LocalDateTime.now());
            limit.setUpdatedAt(LocalDateTime.now());
            limits.add(limit);
        });
        limitDef.getPetBreedLimitsList().forEach(petBreedLimit -> {
            DayHourLimit limit = DayHourLimit.builder().build();
            limit.setType(LimitType.PET_BREED_LIMIT_VALUE);
            limit.setBreedIds(petBreedLimit.getBreedIdsList());
            limit.setCapacity(petBreedLimit.getCapacity());
            limit.setPetTypeId(petBreedLimit.getPetTypeId());
            limit.setIsAllBreed(petBreedLimit.getIsAllBreed());
            limit.setCreatedAt(LocalDateTime.now());
            limit.setUpdatedAt(LocalDateTime.now());
            limits.add(limit);
        });
        return limits;
    }
}
