package com.moego.svc.organization.dto.camera;

import com.moego.idl.models.organization.v1.VisibilityType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class CameraListFilter {
    private IdogcamFilter idogcamFilter;
    private AbckamFilter abckamFilter;
    private CameraFilter cameraFilter;

    @Data
    public static class IdogcamFilter {
        private String kennelId;
        private String erpCode;
    }

    @Data
    public static class AbckamFilter {
        private String abcKamId;
    }

    @Data
    public static class CameraFilter {
        private Boolean isActive;
        private VisibilityType visibilityType;
        private Long relationBusinessId;
    }
}
