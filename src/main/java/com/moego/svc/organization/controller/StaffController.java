package com.moego.svc.organization.controller;

import static com.moego.idl.models.organization.v1.OwnerStaffDef.StaffWithEmailDef;
import static com.moego.svc.organization.enums.DataSourceConst.READER;
import static com.moego.svc.organization.service.StaffAvailabilityDayHourService.dayOfWeek2String;
import static com.moego.svc.organization.service.StaffAvailabilityDayHourService.decodeWeekJson;
import static com.moego.svc.organization.service.StaffInviteLinkService.INVITE_ENTERPRISE_STAFF_LINK_EMAIL_KEY;
import static com.moego.svc.organization.service.StaffInviteLinkService.INVITE_STAFF_LINK_EMAIL_KEY;

import com.google.protobuf.GeneratedMessageV3;
import com.google.type.DayOfWeek;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.StaffEnum;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.ClockInOutStaffDef;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.LocationClockInOutInfoDef;
import com.moego.idl.models.organization.v1.LocationStaffIdsDef;
import com.moego.idl.models.organization.v1.LocationStaffsDef;
import com.moego.idl.models.organization.v1.OwnerStaffDef;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SendInviteLinkParamsDef;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.StaffAccessControlDef;
import com.moego.idl.models.organization.v1.StaffAccessListDef;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.StaffEmailDef;
import com.moego.idl.models.organization.v1.StaffEmployeeCategory;
import com.moego.idl.models.organization.v1.StaffInfoDef;
import com.moego.idl.models.organization.v1.StaffInviteLinkSendMethodType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.StaffShowOnCalendarDef;
import com.moego.idl.models.organization.v1.StaffVanDef;
import com.moego.idl.models.organization.v1.StaffWorkingLocationDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.service.enterprise.v1.EnterpriseServiceGrpc;
import com.moego.idl.service.enterprise.v1.GetEnterpriseRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.organization.v1.CalenderStaff;
import com.moego.idl.service.organization.v1.CheckStaffLoginTimeRequest;
import com.moego.idl.service.organization.v1.CheckStaffLoginTimeResponse;
import com.moego.idl.service.organization.v1.CountStaffWithRoleRequest;
import com.moego.idl.service.organization.v1.CountStaffWithRoleResponse;
import com.moego.idl.service.organization.v1.CreateEnterpriseOwnerRequest;
import com.moego.idl.service.organization.v1.CreateEnterpriseOwnerResponse;
import com.moego.idl.service.organization.v1.CreateEnterpriseStaffRequest;
import com.moego.idl.service.organization.v1.CreateEnterpriseStaffResponse;
import com.moego.idl.service.organization.v1.CreateStaffRecordRequest;
import com.moego.idl.service.organization.v1.CreateStaffRecordResponse;
import com.moego.idl.service.organization.v1.CreateStaffRequest;
import com.moego.idl.service.organization.v1.CreateStaffResponse;
import com.moego.idl.service.organization.v1.DeleteEnterpriseStaffRequest;
import com.moego.idl.service.organization.v1.DeleteEnterpriseStaffResponse;
import com.moego.idl.service.organization.v1.DeleteStaffAvailabilityOverrideRequest;
import com.moego.idl.service.organization.v1.DeleteStaffAvailabilityOverrideResponse;
import com.moego.idl.service.organization.v1.DeleteStaffRequest;
import com.moego.idl.service.organization.v1.DeleteStaffResponse;
import com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest;
import com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeResponse;
import com.moego.idl.service.organization.v1.GetClockInOutStaffsRequest;
import com.moego.idl.service.organization.v1.GetClockInOutStaffsResponse;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffRequest;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffResponse;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffsByAccountIdRequest;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffsByAccountIdResponse;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsRequest;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResponse;
import com.moego.idl.service.organization.v1.GetShowOnCalendarStaffIdsResponse;
import com.moego.idl.service.organization.v1.GetShowOnCalendarStaffsRequest;
import com.moego.idl.service.organization.v1.GetShowOnCalendarStaffsResponse;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityOverrideRequest;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityOverrideResponse;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.organization.v1.GetStaffAvailabilityResponse;
import com.moego.idl.service.organization.v1.GetStaffByCompanyRequest;
import com.moego.idl.service.organization.v1.GetStaffByCompanyResponse;
import com.moego.idl.service.organization.v1.GetStaffByPhoneNumberRequest;
import com.moego.idl.service.organization.v1.GetStaffByPhoneNumberResponse;
import com.moego.idl.service.organization.v1.GetStaffCalenderViewRequest;
import com.moego.idl.service.organization.v1.GetStaffCalenderViewResponse;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailResponse;
import com.moego.idl.service.organization.v1.GetStaffFullDetailRequest;
import com.moego.idl.service.organization.v1.GetStaffFullDetailResponse;
import com.moego.idl.service.organization.v1.GetStaffLoginTimeRequest;
import com.moego.idl.service.organization.v1.GetStaffLoginTimeResponse;
import com.moego.idl.service.organization.v1.GetStaffsByAccountIdRequest;
import com.moego.idl.service.organization.v1.GetStaffsByAccountIdResponse;
import com.moego.idl.service.organization.v1.GetStaffsByRoleRequest;
import com.moego.idl.service.organization.v1.GetStaffsByRoleResponse;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationIdsRequest;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationIdsResponse;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationRequest;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationResponse;
import com.moego.idl.service.organization.v1.InitStaffAvailabilityRequest;
import com.moego.idl.service.organization.v1.InitStaffAvailabilityResponse;
import com.moego.idl.service.organization.v1.ListOwnerStaffInfoRequest;
import com.moego.idl.service.organization.v1.ListOwnerStaffInfoResponse;
import com.moego.idl.service.organization.v1.ListStaffEmailDefsRequest;
import com.moego.idl.service.organization.v1.ListStaffEmailDefsResponse;
import com.moego.idl.service.organization.v1.MigrateStaffDataRequest;
import com.moego.idl.service.organization.v1.MigrateStaffDataResponse;
import com.moego.idl.service.organization.v1.QueryStaffByCompanyIdRequest;
import com.moego.idl.service.organization.v1.QueryStaffByCompanyIdResponse;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsResponse;
import com.moego.idl.service.organization.v1.QueryStaffListByPaginationRequest;
import com.moego.idl.service.organization.v1.QueryStaffListByPaginationResponse;
import com.moego.idl.service.organization.v1.SendInviteLinkRequest;
import com.moego.idl.service.organization.v1.SendInviteLinkResponse;
import com.moego.idl.service.organization.v1.SendInviteStaffLinkRequest;
import com.moego.idl.service.organization.v1.SendInviteStaffLinkResponse;
import com.moego.idl.service.organization.v1.SlotAvailabilityDayList;
import com.moego.idl.service.organization.v1.StaffMappingDef;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.organization.v1.TimeAvailabilityDayList;
import com.moego.idl.service.organization.v1.UnlinkStaffRequest;
import com.moego.idl.service.organization.v1.UnlinkStaffResponse;
import com.moego.idl.service.organization.v1.UpdateAccountLastVisitBusinessRequest;
import com.moego.idl.service.organization.v1.UpdateAccountLastVisitBusinessResponse;
import com.moego.idl.service.organization.v1.UpdateBusinessStaffAvailabilityTypeRequest;
import com.moego.idl.service.organization.v1.UpdateEnterpriseStaffRequest;
import com.moego.idl.service.organization.v1.UpdateEnterpriseStaffResponse;
import com.moego.idl.service.organization.v1.UpdateStaffAvailabilityOverrideRequest;
import com.moego.idl.service.organization.v1.UpdateStaffAvailabilityRequest;
import com.moego.idl.service.organization.v1.UpdateStaffAvailabilityResponse;
import com.moego.idl.service.organization.v1.UpdateStaffRecordRequest;
import com.moego.idl.service.organization.v1.UpdateStaffRecordResponse;
import com.moego.idl.service.organization.v1.UpdateStaffRequest;
import com.moego.idl.service.organization.v1.UpdateStaffResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.params.BatchUpdateOBSettingParam;
import com.moego.server.payment.client.IPaymentPlanClient;
import com.moego.svc.organization.converter.BusinessConvert;
import com.moego.svc.organization.converter.PageConverter;
import com.moego.svc.organization.converter.StaffAccessConverter;
import com.moego.svc.organization.converter.StaffConvert;
import com.moego.svc.organization.converter.StaffLoginTimeConvert;
import com.moego.svc.organization.converter.StaffNotificationConvert;
import com.moego.svc.organization.converter.StaffPayrollSettingConvert;
import com.moego.svc.organization.converter.VanConvert;
import com.moego.svc.organization.dto.BusinessCidDTO;
import com.moego.svc.organization.dto.staff.GetStaffListByPaginationDTO;
import com.moego.svc.organization.dto.staff.MoeStaffLoginTimeDTO;
import com.moego.svc.organization.dto.staff.StaffAccessDTO;
import com.moego.svc.organization.dto.staff.StaffPayrollSettingDTO;
import com.moego.svc.organization.entity.MoeBusiness;
import com.moego.svc.organization.entity.MoeBusinessClockInOutLog;
import com.moego.svc.organization.entity.MoeClockInOutSetting;
import com.moego.svc.organization.entity.MoeCompany;
import com.moego.svc.organization.entity.MoeStaff;
import com.moego.svc.organization.entity.MoeStaffExample;
import com.moego.svc.organization.entity.MoeStaffNotification;
import com.moego.svc.organization.entity.MoeVan;
import com.moego.svc.organization.entity.StaffAvailability;
import com.moego.svc.organization.mapper.MoeBusinessMapper;
import com.moego.svc.organization.mapper.MoeCompanyMapper;
import com.moego.svc.organization.mapper.base.BaseMoeStaffWorkingHourMapper;
import com.moego.svc.organization.params.SaveStaffWorkingLocationsParams;
import com.moego.svc.organization.service.BusinessService;
import com.moego.svc.organization.service.ClockInOutService;
import com.moego.svc.organization.service.CompanyService;
import com.moego.svc.organization.service.InitService;
import com.moego.svc.organization.service.StaffAccessService;
import com.moego.svc.organization.service.StaffAvailabilityDayHourService;
import com.moego.svc.organization.service.StaffAvailabilityService;
import com.moego.svc.organization.service.StaffInviteLinkService;
import com.moego.svc.organization.service.StaffLoginTimeService;
import com.moego.svc.organization.service.StaffMigrateService;
import com.moego.svc.organization.service.StaffNotificationService;
import com.moego.svc.organization.service.StaffPayrollSettingService;
import com.moego.svc.organization.service.StaffService;
import com.moego.svc.organization.service.StaffWorkingHourService;
import com.moego.svc.organization.service.StaffWorkingLocationService;
import com.moego.svc.organization.service.VanService;
import com.moego.svc.organization.utils.AvailabilityDayHourUtils;
import com.moego.svc.organization.utils.PageInfo;
import com.moego.svc.organization.vo.StaffIdAndCompanyIdVO;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Controller
@RequiredArgsConstructor
@Slf4j
public class StaffController extends StaffServiceGrpc.StaffServiceImplBase {
    private final StaffService staffService;
    private final StaffAccessService staffAccessService;
    private final StaffNotificationService staffNotificationService;
    private final StaffWorkingLocationService staffWorkingLocationService;
    private final StaffWorkingHourService staffWorkingHourService;
    private final StaffPayrollSettingService staffPayrollSettingService;
    private final StaffInviteLinkService staffInviteLinkService;
    private final StaffMigrateService staffMigrateService;
    private final ClockInOutService clockInOutService;
    private final StaffLoginTimeService staffLoginTimeService;
    private final StaffAvailabilityService staffAvailabilityService;

    private final BusinessService businessService;
    private final CompanyService companyService;
    private final InitService initService;
    private final VanService vanService;

    private final BusinessConvert businessConvert;
    private final PageConverter pageConverter;

    private final EnterpriseServiceGrpc.EnterpriseServiceBlockingStub enterpriseServiceBlockingStub;
    private final StaffAvailabilityDayHourService staffAvailabilityDayHourService;

    private final MoeBusinessMapper moeBusinessMapper;
    private final BaseMoeStaffWorkingHourMapper baseMoeStaffWorkingHourMapper;

    private final IPaymentPlanClient iPaymentPlanClient;

    private final IGroomingOnlineBookingService onlineBookingService;

    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;
    private final MoeCompanyMapper moeCompanyMapper;

    @Override
    public void createStaff(CreateStaffRequest request, StreamObserver<CreateStaffResponse> responseObserver) {
        var tokenStaffId = request.getTokenStaffId();
        var tokenBusinessId = request.getTokenBusinessId();
        var tokenCompanyId = request.getTokenCompanyId();
        MoeStaff moeStaff = StaffConvert.INSTANCE.toMoeStaff(request.getStaffProfile());
        List<Long> workingLocationIds = new ArrayList<>();
        if (request.hasWorkingLocation()) {
            workingLocationIds.addAll(request.getWorkingLocation().getWorkingLocationIdsList());
            List<Long> allWorkingLocationIds = businessService.getLocationIdListByCompanyId(tokenCompanyId);
            workingLocationIds.retainAll(allWorkingLocationIds); // 过滤掉无效的 working location ids

            if (request.getWorkingLocation().hasWorkingInAllLocations()) {
                boolean workingInAllLocations = request.getWorkingLocation().getWorkingInAllLocations();
                moeStaff.setWorkingInAllLocations(workingInAllLocations);
                if (workingInAllLocations) {
                    workingLocationIds = allWorkingLocationIds;
                }
            }
            if (CollectionUtils.isEmpty(workingLocationIds) && !moeStaff.getWorkingInAllLocations()) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "working location could not be empty");
            }
        } else {
            // 默认把当前 token business 加入 working location，兼容旧版本
            workingLocationIds.add(tokenBusinessId);
        }
        if (request.hasAccessControl()) {
            StaffAccessControlDef accessControl = request.getAccessControl();
            if (accessControl.hasAccessAllWorkingLocationsStaffs()) {
                moeStaff.setAccessAllWorkingLocationsStaff(
                        accessControl.getAccessAllWorkingLocationsStaffs()
                                ? BooleanEnum.VALUE_TRUE
                                : BooleanEnum.VALUE_FALSE);
            }
            if (accessControl.hasIsShowOnCalendar()) {
                moeStaff.setShowOnCalendar(
                        accessControl.getIsShowOnCalendar() ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
            }
            if (accessControl.hasAccessCode()) {
                moeStaff.setAccessCode(accessControl.getAccessCode());
            }
            if (accessControl.hasRequireAccessCode()) {
                moeStaff.setRequireAccessCode(accessControl.getRequireAccessCode());
            }
            if (accessControl.hasStaffShowOnCalendar()) {
                moeStaff.setIsShownOnAllCalendar(
                        accessControl.getStaffShowOnCalendar().getShownOnAllWorkingLocations());
            }
        }
        if (request.hasInviteLink() && request.getInviteLink().hasEmail()) {
            // 有 invite link 时 email 同时保存为 profile_email
            moeStaff.setProfileEmail(request.getInviteLink().getEmail());
        }

        moeStaff.setCreateById((int) tokenStaffId);
        moeStaff.setCompanyId((int) tokenCompanyId);
        Long staffId = staffService.createStaff(moeStaff, tokenCompanyId);
        // 保存 staff working location 关联
        staffWorkingLocationService.saveStaffWorkingLocations(
                new SaveStaffWorkingLocationsParams(staffId, workingLocationIds, tokenStaffId, tokenCompanyId));
        // 初始化 staff 在不同 location 的 working hour
        staffWorkingHourService.initStaffWorkingHour(tokenCompanyId, workingLocationIds, staffId);
        // 保存 access 记录
        if (request.hasAccessControl() && (request.getAccessControl().hasAccessList())
                || request.getAccessControl().hasIsShowOnCalendar()) {
            staffAccessService.saveAccessStaff(
                    staffId,
                    tokenCompanyId,
                    tokenStaffId,
                    request.getAccessControl().getAccessList(),
                    request.getAccessControl().getStaffShowOnCalendar());
        }
        // 保存 notification setting
        if (request.hasNotificationSetting()) {
            staffNotificationService.saveStaffNotificationSetting(
                    staffId, tokenCompanyId, request.getNotificationSetting());
        } else {
            initService.initNotification(staffId, tokenCompanyId);
        }
        // 保存 staff payroll setting
        if (request.hasPayrollSetting()) {
            staffPayrollSettingService.saveStaffPayrollSetting(
                    staffId,
                    StaffPayrollSettingConvert.INSTANCE.toStaffPayrollSettingParams(
                            request.getPayrollSetting(), tokenCompanyId, tokenStaffId));
        } else {
            staffPayrollSettingService.initStaffPayrollSetting(staffId, tokenCompanyId);
        }
        // send invite link
        if (request.hasInviteLink() && request.getInviteLink().getIsSendInviteLink()) {
            staffInviteLinkService.sendInviteStaffLink(staffId, request.getTokenCompanyId(), request.getInviteLink());
        }

        if (request.hasLoginTime()) {
            staffLoginTimeService.upsertStaffLoginTime(
                    StaffLoginTimeConvert.INSTANCE.toMoeStaffLoginTime(request.getLoginTime()).toBuilder()
                            .staffId(staffId)
                            .build());
        }
        responseObserver.onNext(CreateStaffResponse.newBuilder().setId(staffId).build());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffDetail(GetStaffDetailRequest request, StreamObserver<GetStaffDetailResponse> responseObserver) {
        MoeStaff moeStaff = staffService.getStaffById((int) request.getId());
        if (moeStaff == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        if (request.getEnterpriseId() != 0) {
            // check company staff belong to enterprise
            List<Integer> companyIds =
                    companyService.getCompanyListByEnterpriseId(request.getEnterpriseId(), null).getFirst().stream()
                            .map(MoeCompany::getId)
                            .toList();
            if (!Objects.equals(
                            request.getEnterpriseId(),
                            moeStaff.getEnterpriseId().longValue())
                    && !companyIds.contains(moeStaff.getCompanyId())) {
                throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
            }
        } else {
            // check enterprise staff permission,current only support master account
            if (moeStaff.getEnterpriseId() != 0) {
                MoeCompany company = companyService.getCompanyById(request.getCompanyId());
                if (!Objects.equals(company.getEnterpriseId(), moeStaff.getEnterpriseId())) {
                    throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
                }
                // check ok,set company id
                moeStaff.setCompanyId(company.getId());
            }
            if (!Objects.equals(moeStaff.getCompanyId(), (int) request.getCompanyId())) {
                log.error("company id not match, expected{}, got{}", moeStaff.getCompanyId(), request.getCompanyId());
                throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
            }
        }
        StaffModel staffModel = StaffConvert.INSTANCE.toStaffModel(moeStaff);
        if (moeStaff.getCompanyId() > 0) {
            List<LocationBriefView> locationBriefViewList =
                    businessService
                            .getWorkingLocationListByStaffId(
                                    moeStaff, moeStaff.getCompanyId().longValue())
                            .stream()
                            .map(businessConvert::toLocationBriefView)
                            .toList();
            staffModel = staffModel.toBuilder()
                    .addAllWorkingLocationList(locationBriefViewList)
                    .build();
        }
        responseObserver.onNext(
                GetStaffDetailResponse.newBuilder().setStaff(staffModel).build());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffsByAccountId(
            GetStaffsByAccountIdRequest request, StreamObserver<GetStaffsByAccountIdResponse> responseObserver) {
        List<MoeStaff> allStaffs = staffService.queryAllowLoginStaffsByAccountId((int) request.getAccountId());
        List<MoeStaff> companyStaffs = new ArrayList<>();
        List<MoeStaff> enterpriseStaffs = new ArrayList<>();
        for (MoeStaff staff : allStaffs) {
            if (Objects.equals(0, staff.getEnterpriseId())) {
                companyStaffs.add(staff);
            } else {
                enterpriseStaffs.add(staff);
            }
        }

        Set<Integer> companyIdSet = new HashSet<>();
        List<MoeStaff> enterpriseStaffsWithCompanyId = new ArrayList<>();
        for (MoeStaff staff : enterpriseStaffs) {
            var companies = staffService.getVisibleCompaniesByEnterpriseStaff(
                    staff, staff.getEnterpriseId().longValue());
            for (MoeCompany company : companies) {
                // enterprise staff 如果同时是 company 的 owner, 优先用 company owner
                if (!staff.getEmployeeCategory().equals(StaffEnum.EMPLOYEE_CATEGORY_MASTER_OWNER)) {
                    var companyOwnerStaff = companyStaffs.stream()
                            .filter(s -> s.getCompanyId().equals(company.getId()))
                            .filter(s -> s.getEmployeeCategory().equals(StaffEnum.EMPLOYEE_CATEGORY_OWNER))
                            .findAny()
                            .orElse(null);
                    if (companyOwnerStaff != null) {
                        continue;
                    }
                }
                var cloneStaff = StaffConvert.INSTANCE.copyMoeStaff(staff);
                cloneStaff.setCompanyId(company.getId());
                enterpriseStaffsWithCompanyId.add(cloneStaff);
                companyIdSet.add(company.getId());
            }
        }

        companyStaffs = companyStaffs.stream()
                .filter(staff -> !companyIdSet.contains(staff.getCompanyId()))
                .sorted(Comparator.comparingInt(MoeStaff::getAccountSort).reversed())
                .toList();

        // 合并两个列表并排序
        List<MoeStaff> mergeStaffs = new ArrayList<>();
        mergeStaffs.addAll(enterpriseStaffsWithCompanyId);
        mergeStaffs.addAll(companyStaffs);

        // 根据查出来的表排序mergeStaffs
        List<Long> companySortingList = companyService.getCompanySortingList((int) request.getAccountId());
        // 对staffs进行排序
        mergeStaffs.sort(Comparator.comparingInt(s -> {
            int index = companySortingList.indexOf(s.getCompanyId().longValue());
            return index >= 0 ? index : Integer.MAX_VALUE;
        }));

        GetStaffsByAccountIdResponse response = GetStaffsByAccountIdResponse.newBuilder()
                .addAllStaffs(StaffConvert.INSTANCE.toStaffModelList(mergeStaffs))
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffByCompany(
            GetStaffByCompanyRequest request, StreamObserver<GetStaffByCompanyResponse> responseObserver) {

        Integer accountId = (int) request.getAccountId();
        Integer companyId = (int) request.getCompanyId();

        var staffs = staffService.queryAllowLoginStaffsByAccountId(accountId);

        // 优先enterprise staff
        var enterpriseStaffs =
                staffs.stream().filter(staff -> staff.getEnterpriseId() > 0).toList();
        if (!enterpriseStaffs.isEmpty()) {
            // 目前一个account只有一个enterprise，对应一个enterprise staff，一个company只可能属于一个enterprise
            for (MoeStaff staff : enterpriseStaffs) {
                var companies = staffService.getVisibleCompaniesByEnterpriseStaff(
                        staff, staff.getEnterpriseId().longValue());
                for (MoeCompany company : companies) {
                    if (Objects.equals(companyId, company.getId())) {
                        // enterprise staff 如果同时是 company 的 owner, 优先用 company owner
                        if (!staff.getEmployeeCategory().equals(StaffEnum.EMPLOYEE_CATEGORY_OWNER)) {
                            var ownerStaff = staffService.getOwnerStaff(accountId, companyId);
                            if (ownerStaff != null) {
                                responseObserver.onNext(GetStaffByCompanyResponse.newBuilder()
                                        .setStaff(StaffConvert.INSTANCE.toStaffModel(ownerStaff))
                                        .build());
                                responseObserver.onCompleted();
                                return;
                            }
                        }
                        staff.setCompanyId(company.getId());
                        responseObserver.onNext(GetStaffByCompanyResponse.newBuilder()
                                .setStaff(StaffConvert.INSTANCE.toStaffModel(staff))
                                .build());
                        responseObserver.onCompleted();
                        return;
                    }
                }
            }
        }

        var companyStaffs = staffs.stream()
                .filter(staff -> staff.getCompanyId().equals(companyId))
                .toList();
        MoeStaff selectedStaff = null;
        if (!companyStaffs.isEmpty()) {
            // 如果有指定 businessId, 返回 staff businessId 匹配的 staff (用于迁移前的 company)
            if (request.hasBusinessId()) {
                for (MoeStaff staff : companyStaffs) {
                    if (Objects.equals(staff.getBusinessId(), (int) request.getBusinessId())) {
                        selectedStaff = staff;
                        break;
                    }
                }
            } else {
                // 否则返回第一个 staff
                selectedStaff = companyStaffs.get(0);
            }
        }
        GetStaffByCompanyResponse.Builder resBuilder = GetStaffByCompanyResponse.newBuilder();
        if (selectedStaff != null) {
            resBuilder.setStaff(StaffConvert.INSTANCE.toStaffModel(selectedStaff));
        }
        responseObserver.onNext(resBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAccountLastVisitBusiness(
            UpdateAccountLastVisitBusinessRequest request,
            StreamObserver<UpdateAccountLastVisitBusinessResponse> responseObserver) {
        staffService.updateStaffLastVisitBusiness(
                (int) request.getAccountId(), (int) request.getStaffId(), request.getVisitedAt(), (int)
                        request.getLastVisitBusinessId());
        responseObserver.onNext(UpdateAccountLastVisitBusinessResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffFullDetail(
            GetStaffFullDetailRequest request, StreamObserver<GetStaffFullDetailResponse> responseObserver) {
        MoeStaff staff = staffService.getStaffById((int) request.getId());
        if (staff == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        if (staff.getEnterpriseId() != 0) {
            MoeCompany company = companyService.getCompanyById(request.getTokenCompanyId());
            if (!Objects.equals(company.getEnterpriseId(), staff.getEnterpriseId())) {
                throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
            }
            // check ok,set company id
            staff.setCompanyId(company.getId());
        }
        if (!Objects.equals(staff.getCompanyId(), (int) request.getTokenCompanyId())) {
            log.error("company id not match, expected{}, got{}", staff.getCompanyId(), request.getTokenCompanyId());
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        List<Long> workingLocationIds = staffWorkingLocationService.getStaffWorkingLocationIds(
                request.getId(), request.getTokenCompanyId(), staff);
        StaffWorkingLocationDef workingLocations = StaffWorkingLocationDef.newBuilder()
                .setWorkingInAllLocations(staff.getWorkingInAllLocations())
                .addAllWorkingLocationIds(workingLocationIds)
                .build();

        Map<Long, List<Long>> locationStaffIdsMap =
                staffService.getStaffIdsMapByWorkingLocationIds(request.getTokenCompanyId(), null, false);

        List<Long> accessStaffLocations =
                staffAccessService.getAccessStaffLocations(staff.getId().longValue());

        List<StaffAccessDTO> staffAccessList =
                staffAccessService.getAccessStaffIds(request.getId(), null, locationStaffIdsMap);
        Map<Integer, MoeVan> vanMap = vanService.getVanMapByStaffIds(
                staff.getCompanyId().longValue(), Collections.singletonList(staff.getId()));
        StaffAccessControlDef accessControl = StaffAccessControlDef.newBuilder()
                .setIsShowOnCalendar(staff.getShowOnCalendar() == 1)
                .setRequireAccessCode(staff.getRequireAccessCode())
                .setAccessCode(staff.getAccessCode())
                .setAccessList(StaffAccessListDef.newBuilder()
                        .addAllStaffIdsByLocation(
                                StaffAccessConverter.INSTANCE.toStaffAccessByLocationDef(staffAccessList))
                        .build())
                .setAccessAllWorkingLocationsStaffs(
                        Objects.equals(staff.getAccessAllWorkingLocationsStaff(), BooleanEnum.VALUE_TRUE))
                .setStaffShowOnCalendar(StaffShowOnCalendarDef.newBuilder()
                        .setShownOnAllWorkingLocations(staff.getIsShownOnAllCalendar())
                        .addAllLocationIds(accessStaffLocations)
                        .build())
                .build();

        MoeStaffNotification staffNotification = staffNotificationService.getStaffNotification(
                request.getId(), staff.getCompanyId().longValue());
        StaffPayrollSettingDTO staffPayrollSetting =
                staffPayrollSettingService.getStaffPayrollSetting(request.getId(), request.getTokenCompanyId());

        StaffEmailDef staffEmail = staffInviteLinkService.getStaffEmail(staff, INVITE_STAFF_LINK_EMAIL_KEY);
        MoeStaffLoginTimeDTO loginTime =
                staffLoginTimeService.getStaffLoginTime(staff.getId().longValue());

        var van = vanMap.get(staff.getId());
        var response = GetStaffFullDetailResponse.newBuilder()
                .setId(staff.getId())
                .setStaffProfile(StaffConvert.INSTANCE.toStaffBasicView(staff))
                .setWorkingLocation(workingLocations)
                .setAccessControl(accessControl)
                .setNotificationSetting(StaffNotificationConvert.INSTANCE.toDef(staffNotification))
                .setPayrollSetting(StaffPayrollSettingConvert.INSTANCE.toStaffPayrollSettingDef(staffPayrollSetting))
                .setStaffEmail(staffEmail)
                .setStaffVan(StaffVanDef.newBuilder()
                        .setVanId(van == null ? 0 : van.getId())
                        .build())
                .setLoginTime(StaffLoginTimeConvert.INSTANCE.toStaffLoginTimeDef(loginTime))
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateStaff(UpdateStaffRequest request, StreamObserver<UpdateStaffResponse> responseObserver) {
        var updateStaffId = request.getId();
        var tokenStaffId = request.getTokenStaffId();
        var tokenCompanyId = request.getTokenCompanyId();

        MoeStaff staff = staffService.getStaffById((int) updateStaffId);
        if (staff == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }

        MoeStaff updateStaff = MoeStaff.builder().build();
        boolean needUpdate = false;
        if (request.hasStaffProfile() && containFields(request.getStaffProfile())) {
            if (request.getStaffProfile().hasRoleId()) {
                if (request.getStaffProfile().getRoleId() == 0
                        && !PermissionUtil.hasOwnerPermission(staff.getEmployeeCategory())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "can not update role to Owner");
                }
            }
            updateStaff = StaffConvert.INSTANCE.toMoeStaff(request.getStaffProfile());
            needUpdate = true;
        }
        if (request.hasWorkingLocation() && containFields(request.getWorkingLocation())) {
            List<Long> workingLocationIds =
                    new ArrayList<>(request.getWorkingLocation().getWorkingLocationIdsList());
            List<Long> allWorkingLocationIds = businessService.getLocationIdListByCompanyId(tokenCompanyId);
            workingLocationIds.retainAll(allWorkingLocationIds); // 过滤掉无效的 working location ids

            if (request.getWorkingLocation().hasWorkingInAllLocations()) {
                updateStaff.setWorkingInAllLocations(
                        request.getWorkingLocation().getWorkingInAllLocations());
                needUpdate = true;
            }
            if (CollectionUtils.isEmpty(workingLocationIds) && !updateStaff.getWorkingInAllLocations()) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "working location could not be empty");
            }
            staffWorkingLocationService.saveStaffWorkingLocations(new SaveStaffWorkingLocationsParams(
                    updateStaffId, workingLocationIds, tokenStaffId, tokenCompanyId));
            // 初始化 staff 在不同 location 的 working hour
            staffWorkingHourService.initStaffWorkingHour(tokenCompanyId, workingLocationIds, updateStaffId);
        }
        if (request.hasAccessControl() && containFields(request.getAccessControl())) {
            StaffAccessControlDef accessControl = request.getAccessControl();
            if (accessControl.hasAccessAllWorkingLocationsStaffs()) {
                updateStaff.setAccessAllWorkingLocationsStaff(
                        accessControl.getAccessAllWorkingLocationsStaffs()
                                ? BooleanEnum.VALUE_TRUE
                                : BooleanEnum.VALUE_FALSE);
                needUpdate = true;
            }
            if (accessControl.hasIsShowOnCalendar()) {
                updateStaff.setShowOnCalendar(accessControl.getIsShowOnCalendar() ? (byte) 1 : 0);
                needUpdate = true;
            }
            if (accessControl.hasRequireAccessCode()) {
                updateStaff.setRequireAccessCode(accessControl.getRequireAccessCode());
                needUpdate = true;
            }
            if (accessControl.hasAccessCode()) {
                updateStaff.setAccessCode(accessControl.getAccessCode());
                needUpdate = true;
            }
            // 是否展示在所有的business日历中
            if (accessControl.hasStaffShowOnCalendar()) {
                updateStaff.setIsShownOnAllCalendar(
                        accessControl.getStaffShowOnCalendar().getShownOnAllWorkingLocations());
                needUpdate = true;
            }
            if (accessControl.hasAccessList() || accessControl.hasStaffShowOnCalendar()) {
                staffAccessService.saveAccessStaff(
                        updateStaffId,
                        tokenCompanyId,
                        tokenStaffId,
                        accessControl.getAccessList(),
                        accessControl.getStaffShowOnCalendar());
            }
        }
        if (request.hasNotificationSetting() && containFields(request.getNotificationSetting())) {
            staffNotificationService.saveStaffNotificationSetting(
                    updateStaffId, tokenCompanyId, request.getNotificationSetting());
        }
        if (request.hasPayrollSetting() && containFields(request.getPayrollSetting())) {
            staffPayrollSettingService.saveStaffPayrollSetting(
                    updateStaffId,
                    StaffPayrollSettingConvert.INSTANCE.toStaffPayrollSettingParams(
                            request.getPayrollSetting(), tokenCompanyId, tokenStaffId));
        }
        if (request.hasInviteLink() && containFields(request.getInviteLink())) {
            if (request.getInviteLink().hasEmail()) {
                // invite link 同时更新到 profile email
                updateStaff.setProfileEmail(request.getInviteLink().getEmail());
                needUpdate = true;
            }
            if (request.getInviteLink().getIsSendInviteLink()) {
                staffInviteLinkService.sendInviteStaffLink(updateStaffId, tokenCompanyId, request.getInviteLink());
            }
        }
        if (needUpdate) {
            if (!StringUtils.hasText(staff.getInviteCode())) {
                updateStaff.setInviteCode(initService.buildStaffInviteCode());
            }
            updateStaff.setId((int) updateStaffId);
            if (PermissionUtil.hasOwnerPermission(staff.getEmployeeCategory())) {
                updateStaff.setWorkingInAllLocations(null);
                updateStaff.setAccessAllWorkingLocationsStaff(null);
            }
            staffService.updateStaff(updateStaff);

            // staff 更新成功后的处理
            if (request.hasInviteLink() && request.getInviteLink().hasEmail()) {
                String email = request.getInviteLink().getEmail();
                // 如果 email 和缓存的 email 不一致，则删除已有的缓存
                staffInviteLinkService.deleteRecipientEmailCacheIfUpdate(
                        INVITE_STAFF_LINK_EMAIL_KEY, updateStaffId, email);
            }
        }
        if (request.hasLoginTime()) {
            if (StaffEnum.EMPLOYEE_CATEGORY_STAFF.equals(staff.getEmployeeCategory())) {
                // only company staff can set login time
                staffLoginTimeService.upsertStaffLoginTime(
                        StaffLoginTimeConvert.INSTANCE.toMoeStaffLoginTime(request.getLoginTime()).toBuilder()
                                .staffId(updateStaffId)
                                .build());
            }
        }
        responseObserver.onNext(
                UpdateStaffResponse.newBuilder().setSuccess(true).build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteStaff(DeleteStaffRequest request, StreamObserver<DeleteStaffResponse> responseObserver) {
        // 不能删自己
        if (request.getId() == request.getTokenStaffId()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "can not delete self");
        }

        MoeStaff staff = staffService.getStaffById((int) request.getId());
        if (staff == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        // 不能删除 owner, enterprise owner, enterprise staff
        if (staff.getEmployeeCategory() == StaffEmployeeCategory.COMPANY_OWNER_VALUE
                || staff.getEmployeeCategory() == StaffEmployeeCategory.ENTERPRISE_OWNER_VALUE
                || staff.getEmployeeCategory() == StaffEmployeeCategory.ENTERPRISE_STAFF_VALUE) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "can not delete owner");
        }

        boolean success = staffService.deleteStaff(request.getId(), request.getTokenStaffId()) > 0;

        responseObserver.onNext(
                DeleteStaffResponse.newBuilder().setSuccess(success).build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryStaffListByPagination(
            QueryStaffListByPaginationRequest request,
            StreamObserver<QueryStaffListByPaginationResponse> responseObserver) {
        GetStaffListByPaginationDTO pageResult = staffService.getStaffListByPagination(request);
        var tokenCompanyId = request.getTokenCompanyId();
        List<Integer> staffIds =
                pageResult.getStaffs().stream().map(MoeStaff::getId).toList();
        Map<Integer, MoeVan> vanMap = vanService.getVanMapByStaffIds(tokenCompanyId, staffIds);

        Map<Long, StaffEmailDef> staffEmailMap =
                staffInviteLinkService.getStaffEmailMap(pageResult.getStaffs(), INVITE_STAFF_LINK_EMAIL_KEY);

        List<StaffInfoDef> staffInfoList = pageResult.getStaffs().stream()
                .map(staff -> {
                    StaffInfoDef.Builder builder = StaffInfoDef.newBuilder()
                            .setStaffId(staff.getId())
                            .setStaff(StaffConvert.INSTANCE.toStaffBasicView(staff));
                    if (vanMap.containsKey(staff.getId())) {
                        builder.setAssignedVan(VanConvert.INSTANCE.toVanModel(vanMap.get(staff.getId())));
                    }
                    if (staffEmailMap.containsKey(staff.getId().longValue())) {
                        builder.setStaffEmail(staffEmailMap.get(staff.getId().longValue()));
                    }
                    return builder.build();
                })
                .toList();

        QueryStaffListByPaginationResponse response = QueryStaffListByPaginationResponse.newBuilder()
                .addAllStaffs(staffInfoList)
                .setPagination(pageResult.getPagination())
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void queryStaffByIds(
            QueryStaffByIdsRequest request, StreamObserver<QueryStaffByIdsResponse> responseObserver) {
        List<MoeStaff> staffs = staffService.getStaffListByIds(request.getStaffIdsList());
        List<StaffModel> staffModels = StaffConvert.INSTANCE.toStaffModelList(staffs);
        responseObserver.onNext(
                QueryStaffByIdsResponse.newBuilder().addAllStaffs(staffModels).build());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffsByWorkingLocationIds(
            GetStaffsByWorkingLocationIdsRequest request,
            StreamObserver<GetStaffsByWorkingLocationIdsResponse> responseObserver) {
        boolean includeDeleted = request.hasIncludeDeleted() && request.getIncludeDeleted();
        Map<Long, List<Long>> locationStaffIdsMap = staffService.getStaffIdsMapByWorkingLocationIds(
                request.getTokenCompanyId(), request.getBusinessIdsList(), includeDeleted);

        List<Long> staffIds = locationStaffIdsMap.values().stream()
                .flatMap(Collection::stream)
                .distinct()
                .toList();
        Map<Integer, MoeStaff> staffMap = staffService.getStaffMapByIds(staffIds, request.getTokenCompanyId());

        List<LocationStaffsDef> locationStaffs = locationStaffIdsMap.entrySet().stream()
                .map(entry -> {
                    List<StaffModel> staffs = entry.getValue().stream()
                            .filter(staffId -> staffMap.containsKey(staffId.intValue()))
                            .map(staffId -> {
                                MoeStaff staff = staffMap.get(staffId.intValue());
                                if (!includeDeleted && !Objects.equals(staff.getStatus(), StaffEnum.STATUS_NORMAL)) {
                                    return null;
                                }
                                return StaffConvert.INSTANCE.toStaffModel(staff);
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(staffs)) {
                        return null;
                    }
                    staffs.sort(Comparator.comparing(StaffModel::getSort).reversed());
                    return LocationStaffsDef.newBuilder()
                            .setBusinessId(entry.getKey())
                            .addAllStaffs(staffs)
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();
        long totalStaffCount = staffMap.size();
        long totalLocationCount = locationStaffs.size();

        responseObserver.onNext(GetStaffsByWorkingLocationIdsResponse.newBuilder()
                .addAllLocationStaffs(locationStaffs)
                .setTotalStaffCount(totalStaffCount)
                .setTotalLocationCount(totalLocationCount)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getShowOnCalendarStaffs(
            GetShowOnCalendarStaffsRequest request, StreamObserver<GetShowOnCalendarStaffsResponse> responseObserver) {
        var staffMap = staffService.getShowOnCalendarStaffs(
                request.getTokenStaffId(), request.getTokenCompanyId(), request.getBusinessIdsList());

        List<LocationStaffsDef> locationStaffs = staffMap.entrySet().stream()
                .map(entry -> {
                    List<StaffModel> staffs = entry.getValue().stream()
                            .map(StaffConvert.INSTANCE::toStaffModel)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(staffs)) {
                        return null;
                    }
                    staffs.sort(Comparator.comparing(StaffModel::getSort).reversed());
                    return LocationStaffsDef.newBuilder()
                            .setBusinessId(entry.getKey())
                            .addAllStaffs(staffs)
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();

        responseObserver.onNext(GetShowOnCalendarStaffsResponse.newBuilder()
                .addAllLocationStaffs(locationStaffs)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getShowOnCalendarStaffIds(
            GetShowOnCalendarStaffsRequest request,
            StreamObserver<GetShowOnCalendarStaffIdsResponse> responseObserver) {
        var staffIdsMap = staffService.getShowOnCalendarStaffIds(
                request.getTokenStaffId(), request.getTokenCompanyId(), request.getBusinessIdsList());
        List<LocationStaffIdsDef> locationStaffs = staffIdsMap.entrySet().stream()
                .map(entry -> LocationStaffIdsDef.newBuilder()
                        .setBusinessId(entry.getKey())
                        .addAllStaffIds(entry.getValue())
                        .build())
                .toList();

        responseObserver.onNext(GetShowOnCalendarStaffIdsResponse.newBuilder()
                .addAllLocationStaffIds(locationStaffs)
                .build());
        responseObserver.onCompleted();
    }

    /**
     * 迁移 staff 数据，后续可删除
     */
    @Override
    public void migrateStaffData(
            MigrateStaffDataRequest request, StreamObserver<MigrateStaffDataResponse> responseObserver) {
        List<StaffMappingDef> staffMappingDefList = staffMigrateService.migrateStaffData(request.getCompanyId());

        responseObserver.onNext(MigrateStaffDataResponse.newBuilder()
                .addAllStaffMapping(staffMappingDefList)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void countStaffWithRole(
            CountStaffWithRoleRequest request, StreamObserver<CountStaffWithRoleResponse> responseObserver) {
        long staffCount = staffService.countStaffWithRoleId(request.getRoleId());
        responseObserver.onNext(CountStaffWithRoleResponse.newBuilder()
                .setStaffCount(staffCount)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffsByRole(
            GetStaffsByRoleRequest request, StreamObserver<GetStaffsByRoleResponse> responseObserver) {
        var staffs = staffService.getStaffsWithRoleId((int) request.getCompanyId(), request.getRoleId());
        responseObserver.onNext(GetStaffsByRoleResponse.newBuilder()
                .addAllStaffs(StaffConvert.INSTANCE.toStaffModelList(staffs))
                .build());
        responseObserver.onCompleted();
    }

    private Boolean containFields(GeneratedMessageV3 message) {
        if (message == null) {
            return false;
        }
        return message.getAllFields().size() > 0;
    }

    @Override
    public void getStaffsByWorkingLocation(
            GetStaffsByWorkingLocationRequest request,
            StreamObserver<GetStaffsByWorkingLocationResponse> responseObserver) {
        var companyStaffs = staffService.getStaffByCompanyId(request.getCompanyId());
        var staffMap = companyStaffs.stream().collect(Collectors.toMap(MoeStaff::getId, Function.identity()));
        List<MoeStaff> resStaffs = new ArrayList<>();
        Set<Integer> addedStaffIds = new HashSet<>();
        companyStaffs.forEach(staff -> {
            if (staff.getWorkingInAllLocations()) {
                resStaffs.add(staff);
                addedStaffIds.add(staff.getId());
            }
        });
        staffService
                .getStaffIdsMapByWorkingLocationIds(
                        request.getCompanyId(), List.of(request.getWorkingLocationId()), false)
                .getOrDefault(request.getWorkingLocationId(), List.of())
                .forEach(staffId -> {
                    if (staffMap.containsKey(staffId.intValue()) && !addedStaffIds.contains(staffId.intValue())) {
                        resStaffs.add(staffMap.get(staffId.intValue()));
                        addedStaffIds.add(staffId.intValue());
                    }
                });
        List<MoeStaff> staffs = resStaffs.stream()
                .sorted(Comparator.comparing(MoeStaff::getSort).reversed().thenComparing(MoeStaff::getId))
                .toList();
        responseObserver.onNext(GetStaffsByWorkingLocationResponse.newBuilder()
                .addAllStaffs(StaffConvert.INSTANCE.toStaffBasicViewList(staffs))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getEnterpriseStaffsByWorkingLocationIds(
            GetEnterpriseStaffsByWorkingLocationIdsRequest request,
            StreamObserver<GetEnterpriseStaffsByWorkingLocationIdsResponse> responseObserver) {
        var company = companyService.getCompanyById(request.getCompanyId());
        var enterpriseId = company.getEnterpriseId();
        if (enterpriseId.equals(0)) {
            responseObserver.onNext(
                    GetEnterpriseStaffsByWorkingLocationIdsResponse.newBuilder().getDefaultInstanceForType());
            responseObserver.onCompleted();
            return;
        }
        var staffs = staffService.getStaffsByEnterpriseId(enterpriseId.longValue());

        List<MoeStaff> workingAllStaffs = new ArrayList<>();
        List<MoeStaff> notWorkingAllStaffs = new ArrayList<>();

        staffs.forEach(staff -> {
            if (staff.getWorkingInAllLocations()) {
                workingAllStaffs.add(staff);
            } else {
                notWorkingAllStaffs.add(staff);
            }
        });
        var staffWorkingLocationRecordsMap =
                staffWorkingLocationService.getWorkingLocationIdListByStaffIds(notWorkingAllStaffs.stream()
                        .map(moeStaff -> new StaffIdAndCompanyIdVO(moeStaff.getId(), request.getCompanyId()))
                        .collect(Collectors.toList()));
        var allLocations = businessService.getLocationsByCompanyId(request.getCompanyId());
        List<Long> businessIds = new ArrayList<>(request.getBusinessIdsList());
        if (businessIds.isEmpty()) {
            for (MoeBusiness location : allLocations) {
                businessIds.add(location.getId().longValue());
            }
        } else {
            businessIds = businessIds.stream()
                    .filter(businessId -> allLocations.stream()
                            .anyMatch(location -> location.getId().equals(businessId.intValue())))
                    .toList();
        }
        var resBuilder = GetEnterpriseStaffsByWorkingLocationIdsResponse.newBuilder();
        businessIds.forEach(businessId -> {
            LocationStaffsDef.Builder locationStaffsDefBuilder = LocationStaffsDef.newBuilder();
            locationStaffsDefBuilder.setBusinessId(businessId);
            if (!workingAllStaffs.isEmpty()) {
                locationStaffsDefBuilder.addAllStaffs(StaffConvert.INSTANCE.toStaffModelList(workingAllStaffs));
            }
            notWorkingAllStaffs.forEach(staff -> {
                if (staffWorkingLocationRecordsMap.containsKey(staff.getId().longValue())) {
                    if (staffWorkingLocationRecordsMap
                                    .get(staff.getId().longValue())
                                    .contains(businessId)
                            && workingAllStaffs.stream()
                                    .noneMatch(moeStaff -> moeStaff.getId().equals(staff.getId()))) {
                        locationStaffsDefBuilder.addStaffs(StaffConvert.INSTANCE.toStaffModel(staff));
                    }
                }
            });
            resBuilder.addLocationStaffs(locationStaffsDefBuilder.build());
        });
        responseObserver.onNext(resBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询当前 staff 可以打卡的 staff 列表，及这些 staff 的打卡状态和可以打卡的 location id 列表
     * 1. for themselves, 返回自己
     * 2. at working locations, 返回当前 staff working locations 的所有 staff
     */
    @Override
    public void getClockInOutStaffs(
            GetClockInOutStaffsRequest request, StreamObserver<GetClockInOutStaffsResponse> responseObserver) {
        Long tokenStaffId = request.getTokenStaffId();
        Long tokenCompanyId = request.getTokenCompanyId();
        List<Long> staffIds = request.getStaffIdsList();
        String clockInOutDate = request.getDate();
        boolean isForThemselves = request.getIsForThemselves();

        // 先查询当前 staff 的 working location
        MoeStaff tokenStaff = staffService.getStaffById(tokenStaffId);
        List<Long> curStaffLocationIds =
                staffWorkingLocationService.getStaffWorkingLocationIds(tokenStaffId, tokenCompanyId, tokenStaff);
        if (CollectionUtils.isEmpty(curStaffLocationIds)) {
            responseObserver.onNext(GetClockInOutStaffsResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 获取 ClockInOutSetting，检查是否启用了跨天打卡
        MoeClockInOutSetting clockInOutSetting = clockInOutService.getClockInOutSetting(tokenCompanyId);
        boolean enableClockInOutOvernight =
                clockInOutSetting != null && Boolean.TRUE.equals(clockInOutSetting.getEnableClockInOutOvernight());

        // 和当前 staff 相同 working location 的 staffIds
        List<Long> workingStaffIds;
        if (isForThemselves) {
            workingStaffIds = List.of(tokenStaffId);
        } else {
            workingStaffIds = staffService.getStaffIdsByWorkingLocationIds(tokenCompanyId, curStaffLocationIds);
        }
        if (CollectionUtils.isEmpty(staffIds)) {
            // 如果没有传 staffIds，返回所有 workingStaffIds
            staffIds = workingStaffIds;
        } else {
            // 如果传了 staffIds，取交集
            staffIds.retainAll(workingStaffIds);
        }
        // 查询需要的 staff 的 working location ids
        Map<Long, List<Long>> staffWorkingLocationIdsMap =
                staffService.getWorkingLocationIdsMapByStaffIds(tokenCompanyId, staffIds);
        Map<Integer, Map<Integer, List<MoeBusinessClockInOutLog>>> clockInOutLogMap =
                clockInOutService.getClockInOutLogList(tokenCompanyId, staffIds, clockInOutDate).stream()
                        .collect(Collectors.groupingBy(
                                MoeBusinessClockInOutLog::getStaffId,
                                Collectors.groupingBy(MoeBusinessClockInOutLog::getBusinessId)));

        // 如果启用了跨天打卡，获取前一天的打卡记录
        Map<Integer, Map<Integer, List<MoeBusinessClockInOutLog>>> previousDayClockInOutLogMap = new HashMap<>();
        if (enableClockInOutOvernight && StringUtils.hasText(clockInOutDate)) {
            previousDayClockInOutLogMap =
                    clockInOutService.getPreviousDayClockInOutLogList(tokenCompanyId, staffIds, clockInOutDate).stream()
                            .collect(Collectors.groupingBy(
                                    MoeBusinessClockInOutLog::getStaffId,
                                    Collectors.groupingBy(MoeBusinessClockInOutLog::getBusinessId)));
        }

        Map<Integer, MoeStaff> staffMap = staffService.getStaffMapByIds(staffIds, tokenCompanyId);

        List<ClockInOutStaffDef> clockInOutStaffs = new ArrayList<>();
        Map<Integer, Map<Integer, List<MoeBusinessClockInOutLog>>> finalPreviousDayClockInOutLogMap =
                previousDayClockInOutLogMap;
        staffWorkingLocationIdsMap.forEach((staffId, locationIds) -> {
            MoeStaff staff = staffMap.get(staffId.intValue());
            // 过滤掉已经删除的 staff
            if (staff == null || !Objects.equals(staff.getStatus(), StaffEnum.STATUS_NORMAL)) {
                return;
            }
            // 过滤掉当前 staff 没有的 working location，因为当前 staff 没办法给这些 staff 在非 working location 打卡
            List<Long> intersection =
                    locationIds.stream().filter(curStaffLocationIds::contains).toList();
            // 查询 staff 在这些 working location 的打卡记录
            Map<Integer, List<MoeBusinessClockInOutLog>> staffClockInOutLogMap =
                    clockInOutLogMap.getOrDefault(staffId.intValue(), Map.of());
            // 查询 staff 前一天的打卡记录
            Map<Integer, List<MoeBusinessClockInOutLog>> staffPreviousDayClockInOutLogMap =
                    finalPreviousDayClockInOutLogMap.getOrDefault(staffId.intValue(), Map.of());

            List<LocationClockInOutInfoDef> clockInOutInfoList = intersection.stream()
                    .map(locationId -> {
                        LocationClockInOutInfoDef.Builder clockInOutInfoBuilder =
                                LocationClockInOutInfoDef.newBuilder().setLocationId(locationId);
                        List<MoeBusinessClockInOutLog> clockInOutLogList =
                                staffClockInOutLogMap.get(locationId.intValue());
                        if (CollectionUtils.isEmpty(clockInOutLogList)) {
                            // 没有当天打卡记录，如果启用了跨天打卡，检查前一天的记录
                            if (enableClockInOutOvernight) {
                                List<MoeBusinessClockInOutLog> previousDayClockInOutLogList =
                                        staffPreviousDayClockInOutLogMap.get(locationId.intValue());

                                // 处理跨天打卡
                                LocationClockInOutInfoDef overnightInfo = clockInOutService.processOvernightClockInOut(
                                        locationId, previousDayClockInOutLogList);
                                if (overnightInfo != null) {
                                    return overnightInfo;
                                }
                            }

                            // 没有打卡记录，或者前一天的记录不符合跨天打卡条件
                            clockInOutInfoBuilder
                                    .setClockInTime(0)
                                    .setClockOutTime(0)
                                    .setIsClockIn(false)
                                    .setIsClockOut(false);
                        } else {
                            // 取最新的一条打卡记录
                            clockInOutLogList.sort(Comparator.comparingLong(MoeBusinessClockInOutLog::getUpdateTime)
                                    .reversed());
                            MoeBusinessClockInOutLog clockInOutLog = clockInOutLogList.get(0);
                            clockInOutInfoBuilder
                                    .setClockInTime(clockInOutLog.getClockInTime())
                                    .setClockOutTime(clockInOutLog.getClockOutTime())
                                    .setIsClockIn(clockInOutLog.getClockInTime() != 0)
                                    .setIsClockOut(clockInOutLog.getClockOutTime() != 0);
                        }
                        return clockInOutInfoBuilder.build();
                    })
                    .toList();
            clockInOutStaffs.add(ClockInOutStaffDef.newBuilder()
                    .setStaffId(staffId)
                    .setFirstName(staff.getFirstName())
                    .setLastName(staff.getLastName())
                    .setAccessCode(staff.getAccessCode())
                    .setRequireAccessCode(staff.getRequireAccessCode())
                    .setAvatarPath(staff.getAvatarPath())
                    .setColorCode(staff.getColorCode())
                    .addAllLocations(clockInOutInfoList)
                    .setSort(staff.getSort())
                    .build());
        });
        clockInOutStaffs.sort(
                Comparator.comparingInt(ClockInOutStaffDef::getSort).reversed());
        responseObserver.onNext(GetClockInOutStaffsResponse.newBuilder()
                .addAllClockInOutStaffs(clockInOutStaffs)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createEnterpriseOwner(
            CreateEnterpriseOwnerRequest request, StreamObserver<CreateEnterpriseOwnerResponse> responseObserver) {
        MoeStaff.MoeStaffBuilder moeStaff = MoeStaff.builder();
        moeStaff.enterpriseId((int) request.getEnterpriseId());
        moeStaff.accountId((int) request.getAccountId());
        moeStaff.businessId(0);
        moeStaff.companyId(0);
        moeStaff.roleId((int) request.getRoleId());
        moeStaff.firstName(request.getFirstName());
        moeStaff.lastName(request.getLastName());
        moeStaff.employeeCategory((byte) StaffEmployeeCategory.ENTERPRISE_OWNER_VALUE);
        moeStaff.allowLogin(StaffEnum.ALLOW_LOGIN_TRUE);
        moeStaff.inactive(StaffEnum.INACTIVE_FALSE);
        moeStaff.status(StaffEnum.STATUS_NORMAL);
        moeStaff.workingInAllLocations(true);
        moeStaff.accessAllWorkingLocationsStaff((byte) 1);
        moeStaff.showOnCalendar(StaffEnum.SHOW_ON_CALENDAR_TRUE);
        moeStaff.showCalendarStaffAll(StaffEnum.SHOW_CALENDAR_STAFF_ALL_TRUE);
        moeStaff.bookOnlineAvailable((byte) 0);
        // system add
        moeStaff.source(request.getSourceValue());
        Long staffId = staffService.createStaff(moeStaff.build());
        responseObserver.onNext(
                CreateEnterpriseOwnerResponse.newBuilder().setId(staffId).build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryStaffByCompanyId(
            QueryStaffByCompanyIdRequest request, StreamObserver<QueryStaffByCompanyIdResponse> responseObserver) {
        // pagination 传空时，返回所有 staffs
        var pagination =
                request.hasPagination() ? pageConverter.toPageInfo(request.getPagination()) : new PageInfo(1, 1000, 0);
        var pageResult = staffService.getStaffsByCompanyId(request.getCompanyId(), pagination);
        var staffs = pageResult.getFirst();
        var staffIdToWorkingLocationIds =
                businessService.getWorkingLocationIdsByStaffIds(request.getCompanyId(), staffs);
        List<StaffModel> staffModels = pageResult.getFirst().stream()
                .map(staff -> StaffConvert.INSTANCE.toStaffModel(staff).toBuilder()
                        .addAllWorkingLocationList(
                                staffIdToWorkingLocationIds
                                        .getOrDefault(staff.getId().longValue(), List.of())
                                        .stream()
                                        .map(id -> LocationBriefView.newBuilder()
                                                .setId(id)
                                                .build())
                                        .toList())
                        .build())
                .toList();
        responseObserver.onNext(QueryStaffByCompanyIdResponse.newBuilder()
                .addAllStaffs(staffModels)
                .setPagination(pageConverter.toResponse(pageResult.getSecond()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void sendInviteStaffLink(
            SendInviteStaffLinkRequest request, StreamObserver<SendInviteStaffLinkResponse> responseObserver) {
        staffInviteLinkService.sendInviteStaffLink(
                request.getStaffId(),
                request.getCompanyId(),
                SendInviteLinkParamsDef.newBuilder()
                        .setIsSendInviteLink(true)
                        .setEmail(request.getEmail())
                        .setMethodType(StaffInviteLinkSendMethodType.EMAIL)
                        .build());
        responseObserver.onNext(SendInviteStaffLinkResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void listOwnerStaffInfo(
            ListOwnerStaffInfoRequest request, StreamObserver<ListOwnerStaffInfoResponse> responseObserver) {
        if (request.getCompanyIdCount() == 0) {
            responseObserver.onNext(ListOwnerStaffInfoResponse.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        var cIDs = request.getCompanyIdList().stream().map(Long::intValue).collect(Collectors.toList());

        MoeStaffExample example = new MoeStaffExample();
        example.createCriteria()
                .andCompanyIdIn(cIDs)
                .andEmployeeCategoryEqualTo((byte) StaffEmployeeCategory.COMPANY_OWNER_VALUE)
                .andStatusIn(List.of(StaffEnum.STATUS_NORMAL, StaffEnum.STATUS_TEMPORARY));
        List<MoeStaff> staffs = staffService.listStaff(example);
        if (staffs.isEmpty()) {
            responseObserver.onNext(ListOwnerStaffInfoResponse.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }
        Map<Long, OwnerStaffDef.Builder> ownStaffMap = new HashMap<>();

        staffs.forEach(staff -> {
            long companyId = staff.getCompanyId().longValue();
            if (!ownStaffMap.containsKey(companyId)) {
                ownStaffMap.put(companyId, OwnerStaffDef.newBuilder());
            }
            OwnerStaffDef.Builder ownStaffDef = ownStaffMap.get(companyId);

            if (Objects.equals(staff.getStatus(), StaffEnum.STATUS_NORMAL)) {
                StaffEmailDef staffEmail = staffInviteLinkService.getStaffEmail(staff, INVITE_STAFF_LINK_EMAIL_KEY);
                StaffWithEmailDef activeDef = StaffWithEmailDef.newBuilder()
                        .setStaff(StaffConvert.INSTANCE.toStaffModel(staff))
                        .setStaffEmailDef(staffEmail)
                        .build();
                ownStaffDef.setActiveStaff(activeDef);
            } else if (Objects.equals(staff.getStatus(), StaffEnum.STATUS_TEMPORARY)) {
                StaffEmailDef staffEmail = staffInviteLinkService.getStaffEmail(staff, INVITE_STAFF_LINK_EMAIL_KEY);
                StaffModel staffModel = StaffConvert.INSTANCE.toStaffModel(staff);
                StaffWithEmailDef temporaryDef = StaffWithEmailDef.newBuilder()
                        .setStaff(staffModel)
                        .setStaffEmailDef(staffEmail)
                        .build();
                ownStaffDef.setTemporaryStaff(temporaryDef);
            }
        });

        Map<Long, OwnerStaffDef> ownStaffMapBuilt = new HashMap<>();
        ownStaffMap.forEach(
                (companyId, ownStaffDefBuilder) -> ownStaffMapBuilt.put(companyId, ownStaffDefBuilder.build()));

        responseObserver.onNext(ListOwnerStaffInfoResponse.newBuilder()
                .putAllOwnStaffs(ownStaffMapBuilt)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createStaffRecord(
            CreateStaffRecordRequest request, StreamObserver<CreateStaffRecordResponse> responseObserver) {
        MoeStaff.MoeStaffBuilder moeStaff = MoeStaff.builder();
        moeStaff.accountId((int) request.getAccountId());
        moeStaff.businessId(0);
        moeStaff.companyId((int) request.getCompanyId());
        moeStaff.roleId((int) request.getRoleId());
        moeStaff.firstName(request.getFirstName());
        moeStaff.lastName(request.getLastName());
        moeStaff.employeeCategory((byte) request.getEmployeeCategory().getNumber());
        moeStaff.allowLogin(StaffEnum.ALLOW_LOGIN_TRUE);
        moeStaff.inactive(StaffEnum.INACTIVE_FALSE);
        moeStaff.workingInAllLocations(true);
        moeStaff.accessAllWorkingLocationsStaff((byte) 1);
        moeStaff.showOnCalendar(StaffEnum.SHOW_ON_CALENDAR_TRUE);
        moeStaff.showCalendarStaffAll(StaffEnum.SHOW_CALENDAR_STAFF_ALL_TRUE);
        moeStaff.inviteCode(initService.buildStaffInviteCode());
        moeStaff.profileEmail(request.getEmail());
        moeStaff.bookOnlineAvailable((byte) 0);
        if (request.hasStatus()) {
            moeStaff.status((byte) request.getStatus().getNumber());
        } else {
            moeStaff.status(StaffEnum.STATUS_NORMAL);
        }
        Long staffId = staffService.createStaff(moeStaff.build());
        MoeStaff staff = staffService.getStaff(staffId.intValue());
        responseObserver.onNext(CreateStaffRecordResponse.newBuilder()
                .setStaff(StaffConvert.INSTANCE.toStaffModel(staff))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateStaffRecord(
            UpdateStaffRecordRequest request, StreamObserver<UpdateStaffRecordResponse> responseObserver) {

        MoeStaff staff = staffService.getStaff((int) request.getId());
        if (staff == null || !staff.getCompanyId().equals((int) request.getCompanyId())) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }

        MoeStaff updateStaff = MoeStaff.builder().build();
        updateStaff.setId((int) request.getId());
        if (request.hasAccountId()) {
            updateStaff.setAccountId((int) request.getAccountId());
        }
        if (request.hasStatus()) {
            updateStaff.setStatus((byte) request.getStatus().getNumber());
        }
        if (request.hasFirstName()) {
            updateStaff.setFirstName(request.getFirstName());
        }
        if (request.hasLastName()) {
            updateStaff.setLastName(request.getLastName());
        }
        if (request.hasEmail()) {
            updateStaff.setProfileEmail(request.getEmail());
        }
        if (request.hasEmployeeCategory()) {
            updateStaff.setEmployeeCategory((byte) request.getEmployeeCategory().getNumber());
        }
        if (request.hasRoleId()) {
            updateStaff.setRoleId((int) request.getRoleId());
        }
        if (request.hasWorkingInAllLocations()) {
            updateStaff.setWorkingInAllLocations(request.getWorkingInAllLocations());
        }
        long staffId = request.getId();
        staffService.updateStaff(updateStaff);
        MoeStaff s = staffService.getStaff((int) staffId);
        responseObserver.onNext(UpdateStaffRecordResponse.newBuilder()
                .setStaff(StaffConvert.INSTANCE.toStaffModel(s))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getEnterpriseStaffsByAccountId(
            GetEnterpriseStaffsByAccountIdRequest request,
            StreamObserver<GetEnterpriseStaffsByAccountIdResponse> responseObserver) {
        MoeStaffExample example = new MoeStaffExample();
        var criteria = example.createCriteria();
        criteria.andAccountIdEqualTo((int) request.getAccountId()).andEnterpriseIdGreaterThan(0);
        if (!request.getFilter().getIncludeDeleted()) {
            criteria.andStatusEqualTo(StaffEnum.STATUS_NORMAL);
        }
        if (!request.getFilter().getIncludeNotAllowLoginIn()) {
            criteria.andAllowLoginEqualTo(StaffEnum.ALLOW_LOGIN_TRUE);
        }
        var staffs = staffService.listStaff(example);
        responseObserver.onNext(GetEnterpriseStaffsByAccountIdResponse.newBuilder()
                .addAllStaffs(StaffConvert.INSTANCE.toStaffModelList(staffs))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createEnterpriseStaff(
            CreateEnterpriseStaffRequest request, StreamObserver<CreateEnterpriseStaffResponse> responseObserver) {

        MoeStaff moeStaff = StaffConvert.INSTANCE.toMoeStaff(request.getProfile());
        moeStaff.setShowOnCalendar((byte) 0);
        moeStaff.setShowCalendarStaffAll((byte) 1);
        moeStaff.setAccessAllWorkingLocationsStaff((byte) 1);
        moeStaff.setWorkingInAllLocations(true);
        moeStaff.setEmployeeCategory(StaffEnum.EMPLOYEE_CATEGORY_ENTERPRISE_STAFF);
        moeStaff.setEnterpriseId((int) request.getEnterpriseId());
        if (request.hasInviteLink() && containFields(request.getInviteLink())) {
            if (request.getInviteLink().hasEmail()) {
                // invite link 同时更新到 profile email
                moeStaff.setProfileEmail(request.getInviteLink().getEmail());
            }
        }

        Long staffId = staffService.createStaff(moeStaff);

        if (request.hasInviteLink() && containFields(request.getInviteLink())) {
            if (request.getInviteLink().getIsSendInviteLink()) {
                staffInviteLinkService.sendInviteEnterpriseStaffLink(
                        staffId,
                        request.getInviteLink(),
                        enterpriseServiceBlockingStub
                                .getEnterprise(GetEnterpriseRequest.newBuilder()
                                        .setId(request.getEnterpriseId())
                                        .build())
                                .getEnterprise());
            }
        }

        initService.initNotification(staffId, (long) 0);

        MoeStaff staff = staffService.getStaff(staffId.intValue());

        StaffModel staffModel = StaffConvert.INSTANCE.toStaffModel(staff);
        StaffEmailDef staffEmail = staffInviteLinkService.getStaffEmail(staff, INVITE_ENTERPRISE_STAFF_LINK_EMAIL_KEY);

        responseObserver.onNext(CreateEnterpriseStaffResponse.newBuilder()
                .setStaff(staffModel)
                .setStaffEmail(staffEmail)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateEnterpriseStaff(
            UpdateEnterpriseStaffRequest request, StreamObserver<UpdateEnterpriseStaffResponse> responseObserver) {
        var updateStaffId = request.getId();
        MoeStaff staff = staffService.getStaffById(updateStaffId);
        if (staff == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        if (request.getEnterpriseId() != staff.getEnterpriseId()) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "no permission to get staff");
        }

        MoeStaff updateStaff = MoeStaff.builder().build();
        boolean needUpdate = false;
        if (request.hasProfile() && containFields(request.getProfile())) {
            updateStaff = StaffConvert.INSTANCE.toMoeStaff(request.getProfile());
            needUpdate = true;
        }
        if (request.hasInviteLink() && containFields(request.getInviteLink())) {
            if (request.getInviteLink().hasEmail()) {
                // invite link 同时更新到 profile email
                updateStaff.setProfileEmail(request.getInviteLink().getEmail());
                needUpdate = true;
            }
            if (request.getInviteLink().getIsSendInviteLink()) {
                staffInviteLinkService.sendInviteEnterpriseStaffLink(
                        updateStaffId,
                        request.getInviteLink(),
                        enterpriseServiceBlockingStub
                                .getEnterprise(GetEnterpriseRequest.newBuilder()
                                        .setId(request.getEnterpriseId())
                                        .build())
                                .getEnterprise());
            }
        }

        if (needUpdate) {
            if (!StringUtils.hasText(staff.getInviteCode())) {
                updateStaff.setInviteCode(initService.buildStaffInviteCode());
            }
            updateStaff.setId((int) updateStaffId);
            staffService.updateStaff(updateStaff);

            // staff 更新成功后的处理
            if (request.hasInviteLink() && request.getInviteLink().hasEmail()) {
                String email = request.getInviteLink().getEmail();
                // 如果 email 和缓存的 email 不一致，则删除已有的缓存
                staffInviteLinkService.deleteRecipientEmailCacheIfUpdate(
                        INVITE_ENTERPRISE_STAFF_LINK_EMAIL_KEY, updateStaffId, email);
            }
        }

        MoeStaff moeStaff = staffService.getStaff((int) updateStaffId);

        StaffModel staffModel = StaffConvert.INSTANCE.toStaffModel(moeStaff);
        StaffEmailDef staffEmail =
                staffInviteLinkService.getStaffEmail(moeStaff, INVITE_ENTERPRISE_STAFF_LINK_EMAIL_KEY);

        responseObserver.onNext(UpdateEnterpriseStaffResponse.newBuilder()
                .setStaff(staffModel)
                .setStaffEmail(staffEmail)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getEnterpriseStaff(
            GetEnterpriseStaffRequest request, StreamObserver<GetEnterpriseStaffResponse> responseObserver) {

        MoeStaff staff = staffService.getStaffById((int) request.getId());
        if (staff == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        if (request.getEnterpriseId() != staff.getEnterpriseId()) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "no permission to get staff");
        }

        StaffEmailDef staffEmail = staffInviteLinkService.getStaffEmail(staff, INVITE_ENTERPRISE_STAFF_LINK_EMAIL_KEY);
        StaffModel staffModel = StaffConvert.INSTANCE.toStaffModel(staff);

        responseObserver.onNext(GetEnterpriseStaffResponse.newBuilder()
                .setStaff(staffModel)
                .setStaffEmail(staffEmail)
                .build());
        responseObserver.onCompleted();
    }

    public void deleteEnterpriseStaff(
            DeleteEnterpriseStaffRequest request, StreamObserver<DeleteEnterpriseStaffResponse> responseObserver) {
        var deleteStaffId = request.getId();
        MoeStaff staff = staffService.getStaffById(deleteStaffId);
        if (staff == null) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
        }
        if (request.getEnterpriseId() != staff.getEnterpriseId()) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "no permission to get staff");
        }

        staffService.deleteStaff(deleteStaffId, (long) 0);
        responseObserver.onNext(DeleteEnterpriseStaffResponse.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    public void listStaffEmailDefs(
            ListStaffEmailDefsRequest request, StreamObserver<ListStaffEmailDefsResponse> responseObserver) {
        List<MoeStaff> staffs = staffService.getStaffListByIds(request.getStaffIdsList());

        Map<Long, StaffEmailDef> staffEmailMap =
                staffInviteLinkService.getStaffEmailMap(staffs, INVITE_ENTERPRISE_STAFF_LINK_EMAIL_KEY);

        responseObserver.onNext(ListStaffEmailDefsResponse.newBuilder()
                .putAllStaffEmails(staffEmailMap)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void sendInviteLink(SendInviteLinkRequest request, StreamObserver<SendInviteLinkResponse> responseObserver) {

        MoeStaffExample example = new MoeStaffExample();
        var criteria = example.createCriteria();
        criteria.andInviteCodeEqualTo(request.getInviteLink().getInviteCode());

        List<MoeStaff> moeStaffs = staffService.listStaff(example);
        if (moeStaffs.isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "Staff not found");
        }

        MoeStaff staff = moeStaffs.get(0);

        // 权限检查
        if (request.hasEnterpriseId() && staff.getEnterpriseId() != request.getEnterpriseId()) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "No permission to access staff");
        }

        // 发送邀请链接
        SendInviteLinkParamsDef inviteParams = SendInviteLinkParamsDef.newBuilder()
                .setIsSendInviteLink(true)
                .setEmail(request.getInviteLink().getEmail())
                .setMethodType(StaffInviteLinkSendMethodType.EMAIL)
                .build();

        staffInviteLinkService.sendInviteEnterpriseStaffLink(
                staff.getId().longValue(),
                inviteParams,
                enterpriseServiceBlockingStub
                        .getEnterprise(GetEnterpriseRequest.newBuilder()
                                .setId(request.getEnterpriseId())
                                .build())
                        .getEnterprise());

        responseObserver.onNext(SendInviteLinkResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void unlinkStaff(UnlinkStaffRequest request, StreamObserver<UnlinkStaffResponse> responseObserver) {
        MoeStaff staff = staffService.getStaff((int) request.getId());
        // 权限检查
        if (request.hasEnterpriseId() && staff.getEnterpriseId() != request.getEnterpriseId()) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "No permission to access staff");
        }
        staffService.unLinkStaff(staff);
        responseObserver.onNext(UnlinkStaffResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffLoginTime(
            GetStaffLoginTimeRequest request, StreamObserver<GetStaffLoginTimeResponse> responseObserver) {
        if (request.hasCompanyId()) {
            MoeStaff staff = staffService.getStaffById(request.getStaffId());
            if (staff == null) {
                throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
            }
            if (staff.getEnterpriseId() != 0) {
                MoeCompany company = companyService.getCompanyById(request.getCompanyId());
                if (!Objects.equals(company.getEnterpriseId(), staff.getEnterpriseId())) {
                    throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
                }
            } else if (!Objects.equals(staff.getCompanyId(), (int) request.getCompanyId())) {
                throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND, "staff not found");
            }
        }
        var moeLoginTime = staffLoginTimeService.getStaffLoginTime(request.getStaffId());
        var resBuilder = GetStaffLoginTimeResponse.newBuilder();
        resBuilder.setLoginTime(StaffLoginTimeConvert.INSTANCE.toStaffLoginTimeModel(moeLoginTime));
        if (request.getNeedCheckAllowed()) {
            var checkResult = staffLoginTimeService.checkLoginTime(moeLoginTime);
            resBuilder.setIsAllowed(checkResult.isInLoginTime());
            if (!checkResult.isInLoginTime()) {
                resBuilder.setNextLoginTime(checkResult.getNextLoginTime());
            }
        }
        responseObserver.onNext(resBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void checkStaffLoginTime(
            CheckStaffLoginTimeRequest request, StreamObserver<CheckStaffLoginTimeResponse> responseObserver) {
        var moeLoginTime = staffLoginTimeService.getStaffLoginTime(request.getStaffId());
        var checkResult = staffLoginTimeService.checkLoginTime(moeLoginTime);
        var builder = CheckStaffLoginTimeResponse.newBuilder().setIsAllowed(checkResult.isInLoginTime());
        if (!checkResult.isInLoginTime()) {
            builder.setPopUpMessage(checkResult.getPopUpMessage());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffByPhoneNumber(
            GetStaffByPhoneNumberRequest request, StreamObserver<GetStaffByPhoneNumberResponse> responseObserver) {
        MoeStaff staff = staffService.getStaffByPhoneNumber(request.getCompanyId(), request.getPhoneNumber());
        if (staff == null) {
            responseObserver.onNext(GetStaffByPhoneNumberResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        responseObserver.onNext(GetStaffByPhoneNumberResponse.newBuilder()
                .setStaff(StaffConvert.INSTANCE.toStaffModel(staff))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getBusinessStaffAvailabilityType(
            GetBusinessStaffAvailabilityTypeRequest request,
            StreamObserver<GetBusinessStaffAvailabilityTypeResponse> responseObserver) {
        var businessId = request.getBusinessId();
        var availabilityType = businessService.getStaffAvailabilityType(businessId);

        if (Objects.equals(AvailabilityType.BY_SLOT, AvailabilityType.forNumber(availabilityType))) {
            // 检查商家的 pricing 套餐内，是否允许使用 by slot
            boolean allowBySlot = iPaymentPlanClient.checkFeatureCodeIsEnableByBid(
                    Math.toIntExact(businessId), FeatureConst.FC_BOOK_BY_SLOT);
            if (!allowBySlot) {
                businessService.updateBusinessStaffAvailabilityType(businessId, AvailabilityType.BY_TIME.getNumber());

                disableSyncObAvailableTime(businessId);

                availabilityType = AvailabilityType.BY_TIME.getNumber();
            }
        }

        responseObserver.onNext(GetBusinessStaffAvailabilityTypeResponse.newBuilder()
                .setAvailabilityType(AvailabilityType.forNumber(availabilityType))
                .build());
        responseObserver.onCompleted();
    }

    private void disableSyncObAvailableTime(final long businessId) {
        var obSetting = onlineBookingService.getOBSetting(Math.toIntExact(businessId));
        if (BooleanEnum.VALUE_TRUE.equals(obSetting.getAvailableTimeSync())) {
            var newObSetting = new BookOnlineDTO().setBusinessId(Math.toIntExact(businessId));
            newObSetting.setAvailableTimeSync(BooleanEnum.VALUE_FALSE);
            onlineBookingService.batchUpdateOBSetting(new BatchUpdateOBSettingParam()
                    .setBusinessIds(List.of(Math.toIntExact(businessId)))
                    .setUpdateValue(newObSetting));
        }
    }

    @Override
    public void updateBusinessStaffAvailabilityType(
            UpdateBusinessStaffAvailabilityTypeRequest request,
            StreamObserver<UpdateStaffAvailabilityResponse> responseObserver) {
        businessService.updateBusinessStaffAvailabilityType(
                request.getBusinessId(), request.getAvailabilityType().getNumber());
        responseObserver.onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffCalenderView(
            GetStaffCalenderViewRequest request, StreamObserver<GetStaffCalenderViewResponse> responseObserver) {
        var businessId = request.getBusinessId();
        var companyId = request.getCompanyId();
        var dbbusinessStaffIdsMap =
                staffService.getStaffIdsMapByWorkingLocationIds(companyId, List.of(businessId), false);
        var dbStaffIdList = dbbusinessStaffIdsMap.get(businessId);
        if (dbStaffIdList == null) {
            responseObserver.onNext(GetStaffCalenderViewResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        List<StaffAvailability> staffAvailabilities =
                staffAvailabilityService.getStaffAvailabilities(businessId, dbStaffIdList);

        // 初始化不存在的id
        var notExistStaffIds = staffAvailabilityService.initStaffAvailability(
                companyId, businessId, dbStaffIdList, staffAvailabilities);
        if (!notExistStaffIds.isEmpty()) {
            // 如果触发了初始化，则再来一次
            staffAvailabilities = staffAvailabilityService.getStaffAvailabilities(businessId, dbStaffIdList);
        }

        GetStaffCalenderViewResponse.Builder respBuilder = GetStaffCalenderViewResponse.newBuilder();

        // 获取staffId对应的availability slot day表
        Map<Long, List<SlotAvailabilityDay>> slotDayMap = !request.hasAvailabilityType()
                        || request.getAvailabilityType() == AvailabilityType.BY_SLOT
                ? staffAvailabilityDayHourService.getStaffAvailabilitySlotDayDetails(businessId, dbStaffIdList, false)
                : Map.of();

        // 获取staffId对应的availability time day表
        Map<Long, List<TimeAvailabilityDay>> timeDayMap = !request.hasAvailabilityType()
                        || request.getAvailabilityType() == AvailabilityType.BY_TIME
                ? staffAvailabilityDayHourService.getStaffAvailabilityTimeDayDetails(businessId, dbStaffIdList, false)
                : Map.of();

        var startDate = request.getStartDate();
        for (var availability : staffAvailabilities) {
            if (slotDayMap.containsKey(availability.getStaffId())) {
                var builder = CalenderStaff.newBuilder()
                        .setStaffId(availability.getStaffId())
                        .setIsAvailable(true);

                setSlot(availability, slotDayMap, startDate, builder);

                setTime(availability, timeDayMap, startDate, builder);

                respBuilder.addStaffList(builder.build());
            }
        }

        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }

    private static void setTime(
            final StaffAvailability availability,
            final Map<Long, List<TimeAvailabilityDay>> timeDayMap,
            final String startDate,
            final CalenderStaff.Builder builder) {
        if (CollectionUtils.isEmpty(timeDayMap)) {
            return;
        }
        var availabilityDayList = timeDayMap.get(availability.getStaffId());
        var calenderWeekDays = AvailabilityDayHourUtils.getTimeDayByStartEndTime(
                availability.getTimeStartSunday(),
                startDate,
                Objects.requireNonNull(ScheduleType.forNumber(availability.getTimeScheduleType())),
                availabilityDayList);
        builder.setTimeScheduleType(ScheduleType.forNumber(availability.getTimeScheduleType()))
                .setTimeStartSunday(availability.getTimeStartSunday().toString())
                .putAllTimeAvailabilityDayMap(calenderWeekDays);
    }

    private static void setSlot(
            final StaffAvailability availability,
            final Map<Long, List<SlotAvailabilityDay>> slotDayMap,
            final String startDate,
            final CalenderStaff.Builder builder) {
        if (CollectionUtils.isEmpty(slotDayMap)) {
            return;
        }
        var availabilityDayList = slotDayMap.get(availability.getStaffId());
        var calenderWeekDays = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                availability.getSlotStartSunday(),
                startDate,
                Objects.requireNonNull(ScheduleType.forNumber(availability.getSlotScheduleType())),
                availabilityDayList);
        builder.setScheduleType(ScheduleType.forNumber(availability.getSlotScheduleType()))
                .setSlotStartSunday(availability.getSlotStartSunday().toString())
                .putAllSlotAvailabilityDayMap(calenderWeekDays);
    }

    @Override
    public void getStaffAvailability(
            GetStaffAvailabilityRequest request, StreamObserver<GetStaffAvailabilityResponse> responseObserver) {
        if (request.getCompanyId() == 0 || request.getBusinessId() == 0) {
            responseObserver.onNext(GetStaffAvailabilityResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        List<StaffAvailability> staffAvailabilities =
                staffAvailabilityService.getStaffAvailabilities(request.getBusinessId(), request.getStaffIdListList());

        var notExistStaffIds = staffAvailabilityService.initStaffAvailability(
                request.getCompanyId(), request.getBusinessId(), request.getStaffIdListList(), staffAvailabilities);
        if (!notExistStaffIds.isEmpty()) {
            // 如果触发了初始化，则再来一次
            staffAvailabilities = staffAvailabilityService.getStaffAvailabilities(
                    request.getBusinessId(), request.getStaffIdListList());
        }

        Map<Long, List<SlotAvailabilityDay>> slotDayMap = Map.of();
        Map<Long, List<TimeAvailabilityDay>> timeDayMap = Map.of();
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_TIME) {
            timeDayMap = staffAvailabilityDayHourService.getStaffAvailabilityTimeDayDetails(
                    request.getBusinessId(), request.getStaffIdListList(), false);
        }
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_SLOT) {
            slotDayMap = staffAvailabilityDayHourService.getStaffAvailabilitySlotDayDetails(
                    request.getBusinessId(), request.getStaffIdListList(), false);
        }

        GetStaffAvailabilityResponse.Builder respBuilder = GetStaffAvailabilityResponse.newBuilder();

        final Map<Long, List<SlotAvailabilityDay>> finalSlotDayMap = slotDayMap;
        final Map<Long, List<TimeAvailabilityDay>> finalTimeDayMap = timeDayMap;
        staffAvailabilities.forEach(staffAvailability -> {
            var staffAvailabilityBuilder = respBuilder
                    .addStaffAvailabilityListBuilder()
                    .setStaffId(staffAvailability.getStaffId())
                    .setScheduleType(ScheduleType.forNumber(staffAvailability.getSlotScheduleType()))
                    .setSlotStartSunday(staffAvailability.getSlotStartSunday().toString())
                    .setTimeScheduleType(ScheduleType.forNumber(staffAvailability.getTimeScheduleType()))
                    .setTimeStartSunday(staffAvailability.getTimeStartSunday().toString())
                    .setIsAvailable(true);

            List<SlotAvailabilityDay> slotAvailabilityDays = finalSlotDayMap.get(staffAvailability.getStaffId());
            if (slotAvailabilityDays != null) {
                staffAvailabilityBuilder.addAllSlotAvailabilityDayList(slotAvailabilityDays);
            }
            List<TimeAvailabilityDay> timeAvailabilityDays = finalTimeDayMap.get(staffAvailability.getStaffId());
            if (timeAvailabilityDays != null) {
                staffAvailabilityBuilder.addAllTimeAvailabilityDayList(timeAvailabilityDays);
            }
        });

        responseObserver.onNext(respBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void initStaffAvailability(
            final InitStaffAvailabilityRequest request,
            final StreamObserver<InitStaffAvailabilityResponse> responseObserver) {

        if (request.hasCompanyId() && request.hasBusinessId() && request.getStaffIdsCount() > 0) {
            List<StaffAvailability> staffAvailabilities = List.of();
            // staffAvailabilityService.getStaffAvailabilities(request.getBusinessId(), request.getStaffIdsList());
            staffAvailabilityService.initTimeStaffAvailability(
                    request.getCompanyId(), request.getBusinessId(), request.getStaffIdsList(), staffAvailabilities);
            responseObserver.onNext(InitStaffAvailabilityResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        List<BusinessCidDTO> allBusinesses = new ArrayList<>();
        if (request.hasCompanyId() && request.hasBusinessId()) {
            var businessCidDTO = new BusinessCidDTO();
            businessCidDTO.setId(Math.toIntExact(request.getBusinessId()));
            businessCidDTO.setCompanyId(Math.toIntExact(request.getCompanyId()));
            allBusinesses.add(businessCidDTO);
        } else {
            // get all company id + business id
            allBusinesses = moeBusinessMapper.useDataSource(READER).selectAllBusinessCid();
        }

        long startBusinessId;
        long endBusinessId;
        if (request.hasStartBusinessId()) {
            startBusinessId = request.getStartBusinessId();
        } else {
            startBusinessId = 0L;
        }
        if (request.hasEndBusinessId()) {
            endBusinessId = request.getEndBusinessId();
        } else {
            endBusinessId = Long.MAX_VALUE;
        }

        var businessCidDTOs = allBusinesses.stream()
                .filter(dto -> dto.getCompanyId() > 0 && dto.getId() > 0)
                .filter(dto -> dto.getId().longValue() >= startBusinessId
                        && dto.getId().longValue() <= endBusinessId)
                .toList();
        ThreadPool.execute(() -> initData(businessCidDTOs));

        responseObserver.onNext(InitStaffAvailabilityResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void initData(final List<BusinessCidDTO> businessCidDTOs) {
        businessCidDTOs.stream()
                .collect(Collectors.groupingBy(
                        BusinessCidDTO::getCompanyId,
                        Collectors.mapping(
                                BusinessCidDTO::getId, Collectors.mapping(Integer::longValue, Collectors.toList()))))
                .forEach((companyId, businessIds) -> {
                    var staffIdMap =
                            staffService.getStaffIdsMapByWorkingLocationIds(companyId.longValue(), businessIds, false);

                    // 收集所有异步任务
                    List<CompletableFuture<Void>> futures = new ArrayList<>();

                    staffIdMap.forEach((businessId, staffIds) -> {
                        // 为每个业务创建异步任务
                        CompletableFuture<Void> future = CompletableFuture.runAsync(
                                () -> {
                                    log.info(
                                            "init staff availability for company id {}, business id {}",
                                            companyId,
                                            businessId);
                                    try {
                                        List<StaffAvailability> staffAvailabilities = List.of();
                                        // staffAvailabilityService.getStaffAvailabilities(businessId, staffIds);
                                        staffAvailabilityService.initTimeStaffAvailability(
                                                companyId.longValue(), businessId, staffIds, staffAvailabilities);
                                    } catch (Exception e) {
                                        log.error(
                                                "company id: {}, business id: {}, init staff availability error",
                                                companyId,
                                                businessId,
                                                e);
                                    }
                                },
                                ThreadPool.getExecuteExecutor());

                        futures.add(future);
                    });

                    // 等待当前公司的所有业务处理完成
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                            .join();
                });
    }

    /**
     * 处理单个周期的排班设置
     *
     * @param staffAvailabilities 员工可用性列表
     * @param staffId             员工ID
     * @param scheduleType        排班类型
     * @param weekNumber          周数
     * @param weekData            周数据
     * @param businessId          业务ID
     * @param staffMap            员工映射
     */
    private static List<InitStaffAvailabilityResponse.Result> processScheduleWeek(
            final List<com.moego.idl.models.organization.v1.StaffAvailability> staffAvailabilities,
            final Integer staffId,
            final int scheduleType,
            final int weekNumber,
            final String weekData,
            final Integer businessId,
            final Map<Integer, MoeStaff> staffMap) {

        // 只处理符合条件的周期
        if (scheduleType >= weekNumber && weekData != null) {
            var stringListMap = decodeWeekJson(weekData);
            return extracted(staffAvailabilities, staffId, stringListMap, businessId, scheduleType, staffMap);
        }
        return List.of();
    }

    private static List<InitStaffAvailabilityResponse.Result> extracted(
            final List<com.moego.idl.models.organization.v1.StaffAvailability> staffAvailabilities,
            final Integer staffId,
            final Map<String, List<Map<String, Integer>>> stringListMap,
            final Integer businessId,
            final int scheduleType,
            final Map<Integer, MoeStaff> staffMap) {
        // 使用并行流处理员工可用性数据，提高并发处理性能
        return staffAvailabilities.parallelStream()
                .filter(staffAvailability -> staffAvailability.getStaffId() == staffId)
                .filter(com.moego.idl.models.organization.v1.StaffAvailability::getIsAvailable)
                .map(com.moego.idl.models.organization.v1.StaffAvailability::getTimeAvailabilityDayListList)
                .flatMap(Collection::stream)
                .filter(TimeAvailabilityDay::getIsAvailable)
                .map(data -> {
                    // 计算当天的时间范围
                    var timeRange = calculateDayTimeRange(data.getDayOfWeek(), stringListMap);
                    int startTime = timeRange.getStartTime();
                    int endTime = timeRange.getEndTime();

                    // 检查时间设置是否超出范围并记录结果
                    return processTimeHourSettings(
                            data, startTime, endTime, businessId, staffId, scheduleType, staffMap);
                })
                .flatMap(Collection::stream)
                .toList();
    }

    /**
     * 计算指定星期几的时间范围
     *
     * @param dayOfWeek     星期几
     * @param stringListMap 时间映射
     * @return 时间范围对象
     */
    private static TimeRange calculateDayTimeRange(
            final DayOfWeek dayOfWeek, final Map<String, List<Map<String, Integer>>> stringListMap) {

        int startTime = 1439;
        int endTime = 0;

        // 获取当天的时间范围列表
        List<Map<String, Integer>> timeRanges = stringListMap.getOrDefault(dayOfWeek2String(dayOfWeek), List.of());

        if (CollectionUtils.isEmpty(timeRanges)) {
            return new TimeRange(0, 0);
        }

        // 计算最早开始时间和最晚结束时间
        for (Map<String, Integer> timeRange : timeRanges) {
            var currentStartTime = timeRange.get("startTime");
            var currentEndTime = timeRange.get("endTime");
            startTime = Math.min(startTime, currentStartTime);
            endTime = Math.max(endTime, currentEndTime);
        }

        return new TimeRange(startTime, endTime);
    }

    /**
     * 处理时间设置列表，检查是否超出范围
     *
     * @param data         时间可用性数据
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param businessId   业务ID
     * @param staffId      员工ID
     * @param scheduleType 排班类型
     * @param staffMap     员工映射
     */
    private static List<InitStaffAvailabilityResponse.Result> processTimeHourSettings(
            final TimeAvailabilityDay data,
            final int startTime,
            final int endTime,
            final Integer businessId,
            final Integer staffId,
            final int scheduleType,
            final Map<Integer, MoeStaff> staffMap) {

        // 使用并行流处理时间设置列表
        return data.getTimeHourSettingListList().parallelStream()
                .filter(timeHourSetting ->
                        timeHourSetting.getStartTime() < startTime || timeHourSetting.getEndTime() > endTime)
                .map(timeHourSetting -> {
                    // 构建结果对象并添加到结果列表
                    return InitStaffAvailabilityResponse.Result.newBuilder()
                            .setBusinessId(businessId)
                            .setStaffId(staffId)
                            .setScheduleType(scheduleType)
                            .setDayOfWeek(data.getDayOfWeek())
                            .setStaffName(staffMap.get(staffId).getFirstName())
                            .setObStartTime(timeHourSetting.getStartTime())
                            .setObEndTime(timeHourSetting.getEndTime())
                            .setSmStartTime(startTime)
                            .setSmEndTime(endTime)
                            .build();
                })
                .toList();
    }

    /**
     * 时间范围内部类
     */
    private static class TimeRange {
        private final int startTime;
        private final int endTime;

        public TimeRange(int startTime, int endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        public int getStartTime() {
            return startTime;
        }

        public int getEndTime() {
            return endTime;
        }
    }

    @Override
    public void updateStaffAvailability(
            UpdateStaffAvailabilityRequest request, StreamObserver<UpdateStaffAvailabilityResponse> responseObserver) {
        if (request.getStaffAvailabilityListList().isEmpty()) {
            responseObserver.onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var businessId = request.getBusinessId();
        var inputStaffIds = request.getStaffAvailabilityListList().stream()
                .map(StaffAvailabilityDef::getStaffId)
                .toList();

        // update availability 表
        updateAvailabilitiesByInputs(request, businessId, inputStaffIds);

        // update slot/time day 表
        updateSlotAndTimeDaysByInputs(request, inputStaffIds, businessId);
        responseObserver.onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void updateAvailabilitiesByInputs(
            UpdateStaffAvailabilityRequest request, long businessId, List<Long> inputStaffIds) {

        var dbAvailabilitiesMap = staffAvailabilityService.getStaffAvailabilities(businessId, inputStaffIds).stream()
                .collect(Collectors.toMap(StaffAvailability::getStaffId, staffAvailability -> staffAvailability));

        // 可以修改scheduleType
        request.getStaffAvailabilityListList().forEach(availabilityDef -> {
            var dbAvailability = dbAvailabilitiesMap.get(availabilityDef.getStaffId());
            if (dbAvailability != null) {
                boolean needUpdate = false;
                // 如果传入的scheduleType和数据库中的scheduleType不一样，则修改
                if (availabilityDef.hasScheduleType()
                        && availabilityDef.getScheduleType().getNumber() != dbAvailability.getSlotScheduleType()) {
                    dbAvailability.setSlotScheduleType(
                            availabilityDef.getScheduleType().getNumber());
                    dbAvailability.setSlotStartSunday(
                            LocalDate.now().with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.SUNDAY)));
                    needUpdate = true;
                }
                if (availabilityDef.hasTimeScheduleType()
                        && availabilityDef.getTimeScheduleType().getNumber() != dbAvailability.getTimeScheduleType()) {
                    dbAvailability.setTimeScheduleType(
                            availabilityDef.getTimeScheduleType().getNumber());
                    dbAvailability.setTimeStartSunday(
                            LocalDate.now().with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.SUNDAY)));
                    needUpdate = true;
                }
                if (needUpdate) {
                    staffAvailabilityService.update(dbAvailability);
                }
            }
        });
    }

    private void updateSlotAndTimeDaysByInputs(
            UpdateStaffAvailabilityRequest request, List<Long> inputStaffIds, long businessId) {
        // 根据businessId staffId列表，获取所有的slot days, key是staffid, scheduleType, dayOfWeek的组合ID
        // 可以唯一定位到一个Day
        var dbSlotDayMap = staffAvailabilityDayHourService
                .getStaffAvailabilitySlotDays(businessId, inputStaffIds, false)
                // 通过businessId staffId scheduleType dayOfWeek，可以唯一定位到一条数据，
                .stream()
                .collect(Collectors.toMap(
                        staffAvailabilitySlotDay -> AvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                                businessId,
                                staffAvailabilitySlotDay.getStaffId(),
                                staffAvailabilitySlotDay.getScheduleType(),
                                staffAvailabilitySlotDay.getDayOfWeek()),
                        staffAvailabilitySlotDay -> staffAvailabilitySlotDay));
        var dbTimeDayMap = staffAvailabilityDayHourService
                .getStaffAvailabilityTimeDays(businessId, inputStaffIds, false)
                // 通过businessId staffId scheduleType dayOfWeek，可以唯一定位到一条数据，
                .stream()
                .collect(Collectors.toMap(
                        staffAvailabilitySlotDay -> AvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                                businessId,
                                staffAvailabilitySlotDay.getStaffId(),
                                staffAvailabilitySlotDay.getScheduleType(),
                                staffAvailabilitySlotDay.getDayOfWeek()),
                        staffAvailabilitySlotDay -> staffAvailabilitySlotDay));
        staffAvailabilityDayHourService.updateSlotDaysAndHours(
                request.getStaffAvailabilityListList(), dbSlotDayMap, dbTimeDayMap, businessId);
    }

    @Override
    public void deleteStaffAvailabilityOverride(
            DeleteStaffAvailabilityOverrideRequest request,
            StreamObserver<DeleteStaffAvailabilityOverrideResponse> responseObserver) {
        staffAvailabilityDayHourService.deleteOverrideDays(
                request.getBusinessId(),
                request.getStaffId(),
                request.getOverrideDaysList(),
                request.getAvailabilityType());
        responseObserver.onNext(DeleteStaffAvailabilityOverrideResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void updateStaffAvailabilityOverride(
            UpdateStaffAvailabilityOverrideRequest request,
            StreamObserver<UpdateStaffAvailabilityResponse> responseObserver) {
        staffAvailabilityDayHourService.rebuildOverrideDays(
                request.getCompanyId(), request.getBusinessId(), request.getStaffId(), request.getOverrideDaysList());
        staffAvailabilityDayHourService.rebuildTimeOverrideDays(
                request.getCompanyId(),
                request.getBusinessId(),
                request.getStaffId(),
                request.getTimeOverrideDaysList());
        responseObserver.onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getStaffAvailabilityOverride(
            GetStaffAvailabilityOverrideRequest request,
            StreamObserver<GetStaffAvailabilityOverrideResponse> responseObserver) {
        // key staffId
        Map<Long, List<SlotAvailabilityDay>> slotDayMap = Map.of();
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_SLOT) {
            slotDayMap = staffAvailabilityDayHourService.getStaffAvailabilitySlotDayDetails(
                    request.getBusinessId(), request.getStaffIdsList(), true);
        }
        Map<Long, List<TimeAvailabilityDay>> timeDayMap = Map.of();
        if (!request.hasAvailabilityType() || request.getAvailabilityType() == AvailabilityType.BY_TIME) {
            timeDayMap = staffAvailabilityDayHourService.getStaffAvailabilityTimeDayDetails(
                    request.getBusinessId(), request.getStaffIdsList(), true);
        }

        responseObserver.onNext(GetStaffAvailabilityOverrideResponse.newBuilder()
                .putAllOverrideDays(slotDayMap.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> SlotAvailabilityDayList.newBuilder()
                                .addAllSlots(entry.getValue())
                                .build())))
                .putAllTimeOverrideDays(timeDayMap.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> TimeAvailabilityDayList.newBuilder()
                                .addAllSlots(entry.getValue())
                                .build())))
                .build());
        responseObserver.onCompleted();
    }
}
