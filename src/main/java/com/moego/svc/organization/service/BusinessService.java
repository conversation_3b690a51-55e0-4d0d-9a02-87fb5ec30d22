package com.moego.svc.organization.service;

import static com.moego.svc.organization.enums.DataSourceConst.READER;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.pagehelper.PageHelper;
import com.moego.common.enums.StaffEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.online_booking.v1.TimeAvailabilityType;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.sms.v1.BusinessSmsSettingModel;
import com.moego.idl.service.online_booking.v1.GetGroomingServiceAvailabilityRequest;
import com.moego.idl.service.organization.v1.ListLocationsRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.organization.client.AgreementClient;
import com.moego.svc.organization.client.AutoMsgClient;
import com.moego.svc.organization.client.MessageClient;
import com.moego.svc.organization.client.SmsClient;
import com.moego.svc.organization.converter.BusinessConvert;
import com.moego.svc.organization.converter.PageConverter;
import com.moego.svc.organization.dto.BusinessCidDTO;
import com.moego.svc.organization.dto.CompanyAccountIdDTO;
import com.moego.svc.organization.dto.business.ListLocationsDTO;
import com.moego.svc.organization.entity.MoeBusiness;
import com.moego.svc.organization.entity.MoeBusinessExample;
import com.moego.svc.organization.entity.MoeBusinessWorkingHour;
import com.moego.svc.organization.entity.MoeBusinessWorkingHourExample;
import com.moego.svc.organization.entity.MoeStaff;
import com.moego.svc.organization.entity.MoeStaffExample;
import com.moego.svc.organization.entity.MoeStaffWorkingLocation;
import com.moego.svc.organization.entity.MoeStaffWorkingLocationExample;
import com.moego.svc.organization.mapper.MoeBusinessMapper;
import com.moego.svc.organization.mapper.MoeBusinessWorkingHourMapper;
import com.moego.svc.organization.mapper.MoeCompanyMapper;
import com.moego.svc.organization.mapper.MoeStaffMapper;
import com.moego.svc.organization.mapper.MoeStaffWorkingLocationMapper;
import com.moego.svc.organization.utils.PageInfo;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessService {
    private final MoeBusinessMapper moeBusinessMapper;
    private final MoeBusinessWorkingHourMapper moeBusinessWorkingHourMapper;
    private final MoeStaffWorkingLocationMapper moeStaffWorkingLocationMapper;
    private final MoeStaffMapper moeStaffMapper;
    private final MoeCompanyMapper moeCompanyMapper;

    private final InitService initService;
    private final CompanyService companyService;
    private final SmsClient smsClient;
    private final BusinessConvert businessConvert;
    private final AgreementClient agreementClient;
    private final AutoMsgClient autoMsgClient;
    private final MessageClient messageClient;
    private final PageConverter pageConverter;

    private final LoadingCache<Long, Long> businessIdToCompanyIdCache = Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::getCompanyIdRemote);

    private final com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc
                    .OBAvailabilitySettingServiceBlockingStub
            obAvailabilitySettingService;

    public static final String DEFAULT_TIME =
            "{\"monday\":[{\"startTime\":540,\"endTime\":1140}],\"tuesday\":[{\"startTime\":540,\"endTime\":1140}],\"wednesday\":[{\"startTime\":540,\"endTime\":1140}],\"thursday\":[{\"startTime\":540,\"endTime\":1140}],\"friday\":[{\"startTime\":540,\"endTime\":1140}],\"saturday\":[{\"startTime\":540,\"endTime\":1140}],\"sunday\":[{\"startTime\":540,\"endTime\":1140}]}";

    public Long getCompanyId(Long businessId) {
        return businessIdToCompanyIdCache.get(businessId);
    }

    private Long getCompanyIdRemote(Long businessId) {
        MoeBusiness moeBusiness = moeBusinessMapper.useDataSource(READER).selectByPrimaryKey(businessId.intValue());
        return moeBusiness.getCompanyId().longValue();
    }

    public Integer getStaffAvailabilityType(Long businessId) {
        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria().andIdEqualTo(businessId.intValue());
        var results = moeBusinessMapper.selectByExample(example);
        if (!results.isEmpty()) {
            return results.get(0).getStaffAvailabilityType();
        }
        return AvailabilityType.BY_TIME_VALUE;
    }

    public void updateBusinessStaffAvailabilityType(Long businessId, Integer availabilityType) {
        MoeBusiness moeBusiness = moeBusinessMapper.selectByPrimaryKey(businessId.intValue());
        moeBusiness.setStaffAvailabilityType(availabilityType);
        moeBusinessMapper.updateByPrimaryKeySelective(moeBusiness);
    }

    @NotNull
    private TimeAvailabilityType getTimeAvailabilityType(Long companyId, Long businessId) {
        var obAvailability = obAvailabilitySettingService.getGroomingServiceAvailability(
                GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .build())
                        .build());
        return obAvailability.getAvailability().getTimeAvailabilityType();
    }

    public Map<Long, Long> batchGetCompanyId(List<Long> businessIds) {
        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria()
                .andIdIn(businessIds.stream().map(Long::intValue).toList());
        List<MoeBusiness> moeBusinesses =
                moeBusinessMapper.useDataSource(READER).selectByExample(example);
        return moeBusinesses.stream()
                .collect(Collectors.toMap(
                        moeBusiness -> moeBusiness.getId().longValue(),
                        moeBusiness -> moeBusiness.getCompanyId().longValue()));
    }

    public Long createLocation(Long companyId, MoeBusiness moeBusiness) {
        if (!companyService.checkLocationLimit(businessConvert.toBusinessType(moeBusiness.getAppType()), companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "location limit reached");
        }

        Long businessId = initService.initBusiness(companyId, moeBusiness);

        // init agreement
        agreementClient.initBusinessAgreement(companyId, businessId.intValue(), moeBusiness.getBusinessName());

        // 初始化 sms setting
        smsClient.initSmsSetting(companyId);

        // 初始化 message
        autoMsgClient.initBusinessAutoMsg(companyId, businessId);
        messageClient.initBusinessTemplate(companyId, businessId);
        return businessId;
    }

    public void updateLocation(MoeBusiness moeBusiness) {
        moeBusinessMapper.updateByPrimaryKeySelective(moeBusiness);
    }

    public MoeBusiness getLocationDetail(Long businessId) {
        return moeBusinessMapper.useDataSource(READER).selectByPrimaryKey(businessId.intValue());
    }

    public Pair<List<MoeBusiness>, PageInfo> getLocationList(Long companyId, PageInfo pageInfo) {
        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria().andCompanyIdEqualTo(companyId.intValue());
        if (ObjectUtils.isEmpty(pageInfo)) {
            return Pair.of(moeBusinessMapper.useDataSource(READER).selectByExample(example), PageInfo.EMPTY);
        }
        List<MoeBusiness> businesses;
        PageInfo pageResponse;
        try (var page = PageHelper.<MoeBusiness>startPage(pageInfo.pageNum(), pageInfo.pageSize())) {
            page.doSelectPage(() -> moeBusinessMapper.useDataSource(READER).selectByExample(example));

            businesses = page.getResult();
            pageResponse = new PageInfo(page.getPageNum(), page.getPageSize(), (int) page.getTotal());
        }
        return Pair.of(businesses, pageResponse);
    }

    public List<MoeBusiness> getWorkingLocationListByStaffId(MoeStaff staff, Long companyId) {
        MoeBusinessExample businessExample = new MoeBusinessExample();
        if (staff.getWorkingInAllLocations()) {
            businessExample.createCriteria().andCompanyIdEqualTo(companyId.intValue());
        } else {
            MoeStaffWorkingLocationExample example = new MoeStaffWorkingLocationExample();
            example.createCriteria()
                    .andStaffIdEqualTo(staff.getId().longValue())
                    .andCompanyIdEqualTo(companyId)
                    .andDeletedAtIsNull();
            List<Long> workingLocationIdList =
                    moeStaffWorkingLocationMapper.useDataSource(READER).selectByExample(example).stream()
                            .map(MoeStaffWorkingLocation::getWorkingLocationId)
                            .toList();
            if (workingLocationIdList.isEmpty()) {
                throw ExceptionUtil.bizException(
                        Code.CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY, "staff working location list is empty");
            }
            businessExample
                    .createCriteria()
                    .andIdIn(workingLocationIdList.stream().map(Long::intValue).toList());
        }

        return moeBusinessMapper.useDataSource(READER).selectByExample(businessExample);
    }

    public List<Long> getLocationIdListByCompanyId(Long companyId) {
        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria().andCompanyIdEqualTo(companyId.intValue());
        return moeBusinessMapper.selectByExample(example).stream()
                .map(MoeBusiness::getId)
                .map(Long::valueOf)
                .toList();
    }

    public Long getLocationCount(Long companyId) {
        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria().andCompanyIdEqualTo(companyId.intValue());
        return moeBusinessMapper.countByExample(example);
    }

    public String getBusinessAssignedPhoneNumber(Long businessId) {
        BusinessSmsSettingModel businessSmsSettingModel = smsClient.getSmsSetting(businessId);
        return businessSmsSettingModel.getTwilioNumber();
    }

    public Map<Long, String> listBusinessAssignedPhoneNumber(List<Long> businessIds) {
        List<BusinessSmsSettingModel> businessSmsSettingModels = smsClient.listSmsSettings(businessIds);

        Map<Long, String> businessIdToPhoneNumber = new HashMap<>();

        if (!businessSmsSettingModels.isEmpty()) {
            businessSmsSettingModels.forEach(businessSmsSettingModel -> {
                businessIdToPhoneNumber.put(
                        businessSmsSettingModel.getBusinessId(), businessSmsSettingModel.getTwilioNumber());
                log.info(
                        "businessId: {}, twilioNumber: {}",
                        businessSmsSettingModel.getBusinessId(),
                        businessSmsSettingModel.getTwilioNumber());
            });
        }
        return businessIdToPhoneNumber;
    }

    public String getWorkingHours(Long businessId) {
        String workingHourConfig;
        // 这里是懒加载模式的初始化：先从数据库中读，如果读不到的话，就返回默认值，并且写入数据库，完成初始化
        MoeBusinessWorkingHourExample example = new MoeBusinessWorkingHourExample();
        example.createCriteria().andBusinessIdEqualTo(businessId.intValue());
        List<MoeBusinessWorkingHour> moeBusinessWorkingHours =
                moeBusinessWorkingHourMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(moeBusinessWorkingHours)) {
            workingHourConfig = DEFAULT_TIME;
        } else {
            workingHourConfig = moeBusinessWorkingHours.get(0).getTimeData();
        }
        return workingHourConfig;
    }

    public Map<Long /*business id*/, String> getWorkingHoursList(Long companyId) {
        MoeBusinessWorkingHourExample example = new MoeBusinessWorkingHourExample();
        example.createCriteria().andCompanyIdEqualTo(companyId);
        List<MoeBusinessWorkingHour> moeBusinessWorkingHours =
                moeBusinessWorkingHourMapper.selectByExampleWithBLOBs(example);
        return moeBusinessWorkingHours.stream()
                .map(moeBusinessWorkingHour -> Pair.of(
                        moeBusinessWorkingHour.getBusinessId().longValue(), moeBusinessWorkingHour.getTimeData()))
                .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond));
    }

    public void saveWorkingHoursConfig(Long businessId, String workingHoursDTOConfig) {
        MoeBusinessWorkingHourExample example = new MoeBusinessWorkingHourExample();
        example.createCriteria().andBusinessIdEqualTo(businessId.intValue());
        MoeBusinessWorkingHour moeBusinessWorkingHour = MoeBusinessWorkingHour.builder()
                .businessId(businessId.intValue())
                .timeData(workingHoursDTOConfig)
                .build();
        moeBusinessWorkingHourMapper.updateByExampleSelective(moeBusinessWorkingHour, example);
    }

    public List<MoeBusiness> getLocationsByCompanyId(Long companyId) {
        var example = new MoeBusinessExample();
        example.createCriteria().andCompanyIdEqualTo(companyId.intValue());
        return moeBusinessMapper.selectByExample(example);
    }

    public void updateLocationsByCompanyId(Long companyId, MoeBusiness moeBusiness) {
        var example = new MoeBusinessExample();
        example.createCriteria().andCompanyIdEqualTo(companyId.intValue());
        moeBusinessMapper.updateByExampleSelective(moeBusiness, example);
    }

    public Map<Long, List<MoeBusiness>> getLocationsByCompanies(List<Long> companyIds) {
        var example = new MoeBusinessExample();
        example.createCriteria()
                .andCompanyIdIn(companyIds.stream().map(Long::intValue).toList());
        List<MoeBusiness> businesses = moeBusinessMapper.selectByExample(example);
        Map<Long, List<MoeBusiness>> result = new HashMap<>();
        businesses.forEach(business -> {
            if (result.containsKey(business.getCompanyId().longValue())) {
                result.get(business.getCompanyId().longValue()).add(business);
            } else {
                List<MoeBusiness> l = new ArrayList<>();
                l.add(business);
                result.put(business.getCompanyId().longValue(), l);
            }
        });
        return result;
    }

    public List<MoeBusiness> getLocationsByIds(List<Long> businessIds) {
        var example = new MoeBusinessExample();
        example.createCriteria()
                .andIdIn(businessIds.stream().map(Long::intValue).toList());
        return moeBusinessMapper.useDataSource(READER).selectByExample(example);
    }

    public Map<Long, List<Long>> batchGetBusinessIdsByCompanyIds(List<Long> companyIds) {
        var example = new MoeBusinessExample();
        example.createCriteria()
                .andCompanyIdIn(companyIds.stream().map(Long::intValue).toList());
        List<MoeBusiness> businesses = moeBusinessMapper.useDataSource(READER).selectByExample(example);
        return businesses.stream()
                .collect(Collectors.groupingBy(
                        moeBusiness -> moeBusiness.getCompanyId().longValue(),
                        Collectors.mapping(moeBusiness -> moeBusiness.getId().longValue(), Collectors.toList())));
    }

    public Map<Long, Long> batchGetOwnerAccountIdByBusinessIds(List<Long> businessIds) {
        List<MoeBusiness> businesses = getLocationsByIds(businessIds);
        var businessMap = businesses.stream().collect(Collectors.toMap(MoeBusiness::getId, Function.identity()));

        var companyIdList = businesses.stream().map(MoeBusiness::getCompanyId).toList();

        MoeStaffExample example = new MoeStaffExample();
        example.createCriteria()
                .andCompanyIdIn(companyIdList.stream().toList())
                .andEmployeeCategoryEqualTo(StaffEnum.EMPLOYEE_CATEGORY_OWNER)
                .andStatusEqualTo(StaffEnum.STATUS_NORMAL);
        var staffs = moeStaffMapper.selectByExample(example);
        Map<Integer, Integer> accountIdByCompanyIdMap = staffs.stream()
                .collect(Collectors.toMap(MoeStaff::getCompanyId, MoeStaff::getAccountId, (v1, v2) -> v1));

        return businessMap.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getKey().longValue(), entry -> {
                    var companyId = entry.getValue().getCompanyId();
                    return accountIdByCompanyIdMap.getOrDefault(companyId, 0).longValue();
                }));
    }

    public Map<Long, Long> getAllOwnerAccountIdByBusinessIds() {
        List<BusinessCidDTO> allBusinesses =
                moeBusinessMapper.useDataSource(READER).selectAllBusinessCid();
        List<CompanyAccountIdDTO> allCompanies =
                moeCompanyMapper.useDataSource(READER).selectAllCompanyAccountId();

        Map<Integer, CompanyAccountIdDTO> idToCompany =
                allCompanies.stream().collect(Collectors.toMap(CompanyAccountIdDTO::getId, Function.identity()));

        Map<Long, Long> result = new HashMap<>();
        for (BusinessCidDTO business : allBusinesses) {
            CompanyAccountIdDTO company = idToCompany.get(business.getCompanyId());
            long accountId = 0;
            if (company != null) {
                accountId = company.getAccountId().longValue();
            }
            result.put(business.getId().longValue(), accountId);
        }
        return result;
    }

    public Map<Long, List<Long>> getWorkingLocationIdsByStaffIds(Long companyId, List<MoeStaff> staffs) {
        Map<Long, List<Long>> finalWorkingRecords = new HashMap<>();
        var notAllLocationStaffIds = staffs.stream()
                .filter(staff -> !staff.getWorkingInAllLocations())
                .map(staff -> staff.getId().longValue())
                .toList();
        log.info("notAllLocationStaffIds: {}", notAllLocationStaffIds);
        if (!CollectionUtils.isEmpty(notAllLocationStaffIds)) {
            MoeStaffWorkingLocationExample example = new MoeStaffWorkingLocationExample();
            example.createCriteria()
                    .andStaffIdIn(notAllLocationStaffIds)
                    .andCompanyIdEqualTo(companyId)
                    .andDeletedAtIsNull();
            List<MoeStaffWorkingLocation> workingLocations = moeStaffWorkingLocationMapper.selectByExample(example);
            workingLocations.forEach(workingLocation -> {
                var staffId = workingLocation.getStaffId().longValue();
                var locationId = workingLocation.getWorkingLocationId().longValue();
                if (finalWorkingRecords.containsKey(staffId)) {
                    finalWorkingRecords.get(staffId).add(locationId);
                } else {
                    List<Long> locationIds = new ArrayList<>();
                    locationIds.add(locationId);
                    finalWorkingRecords.put(staffId, locationIds);
                }
            });
        }
        log.info("finalWorkingRecords: {}", finalWorkingRecords);
        var allLocationStaffIds = staffs.stream()
                .filter(MoeStaff::getWorkingInAllLocations)
                .map(MoeStaff::getId)
                .toList();
        log.info("allLocationStaffIds: {}", allLocationStaffIds);
        if (!CollectionUtils.isEmpty(allLocationStaffIds)) {
            var allLocations = getLocationsByCompanyId(companyId);
            var allLocationIds =
                    allLocations.stream().map(b -> b.getId().longValue()).toList();
            allLocationStaffIds.forEach(staffId -> finalWorkingRecords.put(staffId.longValue(), allLocationIds));
        }
        log.info("finalWorkingRecords: {}", finalWorkingRecords);
        return finalWorkingRecords;
    }

    public ListLocationsDTO listLocations(ListLocationsRequest request) {
        MoeBusinessExample example = new MoeBusinessExample();
        var criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(request.getFilter().getCompanyIdsList())) {
            criteria.andCompanyIdIn(request.getFilter().getCompanyIdsList().stream()
                    .map(Long::intValue)
                    .toList());
        }
        if (!CollectionUtils.isEmpty(request.getFilter().getIdsList())) {
            criteria.andIdIn(request.getFilter().getIdsList().stream()
                    .map(Long::intValue)
                    .toList());
        }
        List<MoeBusiness> businesses;
        if (!request.hasPagination()) {
            businesses = moeBusinessMapper.selectByExample(example);
            return new ListLocationsDTO()
                    .setLocations(businesses)
                    .setPagination(PaginationResponse.newBuilder()
                            .setTotal(businesses.size())
                            .setPageSize(businesses.size())
                            .setPageNum(1)
                            .build());
        }
        PageInfo pageResponse;
        try (var page = PageHelper.<MoeBusiness>startPage(
                request.getPagination().getPageNum(), request.getPagination().getPageSize())) {
            page.doSelectPage(() -> moeBusinessMapper.selectByExample(example));
            businesses = page.getResult();
            pageResponse = new PageInfo(page.getPageNum(), page.getPageSize(), (int) page.getTotal());
        }
        return new ListLocationsDTO().setLocations(businesses).setPagination(pageConverter.toResponse(pageResponse));
    }
}
