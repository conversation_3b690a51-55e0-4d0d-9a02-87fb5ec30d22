package com.moego.svc.organization.service;

import com.moego.api.thirdparty.IIdogcamClient;
import com.moego.api.thirdparty.dto.IDogCamCameraDto;
import com.moego.idl.service.file.v2.FileServiceGrpc;
import com.moego.svc.organization.dto.CameraSourceDto;
import com.moego.svc.organization.dto.camera.CameraListFilter;
import com.moego.svc.organization.entity.Camera;
import com.moego.svc.organization.entity.IdogcamConfig;
import com.moego.svc.organization.entity.IdogcamConfigExample;
import com.moego.svc.organization.mapper.IdogcamConfigMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Idogcam website:<a href="https://idogcam.com/">idogcam</a>
 * 另一个摄像头厂商，我们集成了他们的 camera 部分 api
 * 可以支持我们的商家和他们 camera 搭配
 */
@Service
@RequiredArgsConstructor
public class CameraIdogcamService {

    private final IIdogcamClient iIdogcamClient;
    private final IdogcamConfigMapper idogcamConfigMapper;
    private final FileServiceGrpc.FileServiceBlockingStub fileService;

    private static final String GENERATE_FILE_NAME = "idogcam%s%s.html";
    private static final String OWNER_TYPE_COMPANY = "company";
    private static final String IDOGCAM_HTML_LINK = "https://idogcam.com/idogcamviewer.php?id=%s&idogcamauth=%s";

    @Value("${camera.idogcam.key}")
    private String idogcamKey;

    public List<CameraSourceDto> fetchNewestCameraList(Long companyId) {
        // query config
        var example = new IdogcamConfigExample();
        var criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(companyId).andIsActiveEqualTo(true);
        var idogcamConfigs = idogcamConfigMapper.selectByExample(example);

        // fetch camera list
        List<CameraSourceDto> cameraList = new ArrayList<>();
        for (IdogcamConfig idogcamConfig : idogcamConfigs) {
            cameraList.addAll(
                    iIdogcamClient
                            .queryCameraList(idogcamKey, idogcamConfig.getKennelId(), idogcamConfig.getErpCode())
                            .stream()
                            .map(dto -> convertDtoToCamera(idogcamConfig.getId(), dto))
                            .toList());
        }
        // convert to camera
        return cameraList;
    }

    /**
     * 异步生成并记录 html
     *
     * @param camera
     */
    public String generateAndRecordHtml(Camera camera) {
        return String.format(IDOGCAM_HTML_LINK, camera.getOriginCameraId(), camera.getAuth());
    }

    private CameraSourceDto convertDtoToCamera(Long configId, IDogCamCameraDto dto) {
        return CameraSourceDto.builder()
                .configId(configId)
                .originCameraId(dto.getId())
                .originCameraTitle(dto.getTitle())
                .auth(dto.getAuth())
                .build();
    }

    public List<Long> getConfigIdByFilter(long companyId, CameraListFilter.IdogcamFilter idogcamFilter) {
        if (!StringUtils.hasText(idogcamFilter.getKennelId()) && !StringUtils.hasText(idogcamFilter.getErpCode())) {
            return List.of();
        }
        var example = new IdogcamConfigExample();
        var criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(companyId);
        if (StringUtils.hasText(idogcamFilter.getKennelId())) {
            criteria.andKennelIdEqualTo(idogcamFilter.getKennelId());
        }
        if (StringUtils.hasText(idogcamFilter.getErpCode())) {
            criteria.andErpCodeEqualTo(idogcamFilter.getErpCode());
        }
        var idogcamConfigs = idogcamConfigMapper.selectByExample(example);
        return idogcamConfigs.stream().map(IdogcamConfig::getId).collect(Collectors.toList());
    }

    public List<Long> getConfigIdByRelationBusinessId(long companyId, long businessId) {
        var example = new IdogcamConfigExample();
        var criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(companyId);
        criteria.andBusinessIdIn(List.of(0L, businessId));
        var idogcamConfigs = idogcamConfigMapper.selectByExample(example);
        return idogcamConfigs.stream().map(IdogcamConfig::getId).collect(Collectors.toList());
    }
}
