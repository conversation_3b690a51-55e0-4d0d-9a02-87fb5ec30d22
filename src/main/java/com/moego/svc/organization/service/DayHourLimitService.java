package com.moego.svc.organization.service;

import com.moego.svc.organization.entity.DayHourLimit;
import com.moego.svc.organization.entity.DayHourLimitExample;
import com.moego.svc.organization.mapper.base.BaseDayHourLimit;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DayHourLimitService {
    private final BaseDayHourLimit dayHourLimitMapper;

    public Map<Long, DayHourLimit> getDayHourLimitMapByLimitIds(List<Long> limitIds) {
        if (limitIds.isEmpty()) {
            return Map.of();
        }
        DayHourLimitExample example = new DayHourLimitExample();
        var criteria = example.createCriteria();
        criteria.andIdIn(limitIds);
        return dayHourLimitMapper.selectByExampleWithBLOBs(example).stream()
                .collect(Collectors.toMap(DayHourLimit::getId, Function.identity()));
    }
    // 删除limit ids
    public void deleteByIds(List<Long> limitIds) {
        if (limitIds.isEmpty()) {
            return;
        }
        DayHourLimitExample example = new DayHourLimitExample();
        var criteria = example.createCriteria();
        criteria.andIdIn(limitIds);
        dayHourLimitMapper.deleteByExample(example);
    }

    // 创建多个limit
    public void createDayHourLimits(List<DayHourLimit> dayHourLimits) {
        if (dayHourLimits.isEmpty()) {
            return;
        }
        for (var dayHourLimit : dayHourLimits) {
            dayHourLimitMapper.insertSelective(dayHourLimit);
        }
    }
}
