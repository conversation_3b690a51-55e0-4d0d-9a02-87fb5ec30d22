package com.moego.svc.organization.service;

import com.moego.idl.models.organization.v1.CameraConfigType;
import com.moego.idl.models.organization.v1.OriginStatus;
import com.moego.idl.models.organization.v1.VisibilityType;
import com.moego.svc.organization.converter.CameraConvert;
import com.moego.svc.organization.dto.CameraSourceDto;
import com.moego.svc.organization.dto.camera.CameraListFilter;
import com.moego.svc.organization.entity.Camera;
import com.moego.svc.organization.entity.CameraExample;
import com.moego.svc.organization.mapper.CameraMapper;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class CameraService {

    private final ******************** idogcamService;
    private final CameraAbckamService abckamService;
    private final CameraConvert cameraConvert;
    private final CameraMapper cameraMapper;
    private final ******************** cameraIdogcamService;

    public List<Camera> getCameraListByFilter(Long companyId, CameraListFilter.CameraFilter cameraFilter) {
        if (cameraFilter == null) {
            return List.of();
        }
        // 初始化 Example 和 Criteria
        var example = new CameraExample();
        var criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(companyId).andOriginStatusEqualTo(OriginStatus.VALID_VALUE);
        Optional.ofNullable(cameraFilter.getIsActive()).ifPresent(criteria::andIsActiveEqualTo);
        Optional.ofNullable(cameraFilter.getVisibilityType())
                .map(VisibilityType::getNumber)
                .ifPresent(criteria::andVisibilityTypeEqualTo);
        // 查询结果
        var cameraList = cameraMapper.selectByExample(example);

        // 根据 relationBusinessId 过滤 camera
        if (cameraFilter.getRelationBusinessId() == null) {
            return cameraList;
        }
        Set<Long> idogcamConfigIds = new HashSet<>(
                idogcamService.getConfigIdByRelationBusinessId(companyId, cameraFilter.getRelationBusinessId()));
        Set<Long> abdkamConfigIds = new HashSet<>(
                abckamService.getConfigIdByRelationBusinessId(companyId, cameraFilter.getRelationBusinessId()));
        if (idogcamConfigIds.isEmpty() && abdkamConfigIds.isEmpty()) {
            return List.of();
        }
        return cameraList.stream()
                .filter(camera -> {
                    var type = camera.getType();
                    Long configId = camera.getConfigId();
                    return (type == CameraConfigType.IDOGCAM.getNumber() && idogcamConfigIds.contains(configId))
                            || (type == CameraConfigType.ABCKAM.getNumber() && abdkamConfigIds.contains(configId));
                })
                .toList();
    }

    /**
     * 根据 filter 查询 camera list
     * 两个 _Filter 互斥，不会同时存在
     *
     * @param companyId company id
     * @param filter    request filer
     */
    public List<Camera> getCameraListByFilter(Long companyId, CameraListFilter filter) {
        if (filter == null) {
            return List.of();
        }
        // get filter
        var iDogCamFilter = filter.getIdogcamFilter();
        var abckamFilter = filter.getAbckamFilter();
        // 初始化 Example 和 Criteria
        var example = new CameraExample();
        var criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(companyId).andOriginStatusEqualTo(OriginStatus.VALID_VALUE);

        // 处理不同的 filter（互斥逻辑）
        if (iDogCamFilter != null) {
            initIdogcamCameraList(companyId);
            if (iDogCamFilter.getKennelId() != null || iDogCamFilter.getErpCode() != null) {
                var configIds = idogcamService.getConfigIdByFilter(companyId, iDogCamFilter);
                if (CollectionUtils.isEmpty(configIds)) {
                    return List.of();
                }
                criteria.andConfigIdIn(configIds);
            }
        } else if (abckamFilter != null) {
            initAbckamCameraList(companyId);
            if (abckamFilter.getAbcKamId() != null) {
                var configIds = abckamService.getConfigIdByFilter(companyId, abckamFilter);
                if (CollectionUtils.isEmpty(configIds)) {
                    return List.of();
                }
                criteria.andConfigIdIn(configIds);
            }
        }
        return cameraMapper.selectByExample(example);
    }

    public void initIdogcamCameraList(Long companyId) {
        var newestCameras = idogcamService.fetchNewestCameraList(companyId);
        saveCameraList(companyId, CameraConfigType.IDOGCAM.getNumber(), newestCameras);
    }

    public void initAbckamCameraList(Long companyId) {
        var newestCameras = abckamService.fetchNewestCameraList(companyId);
        saveCameraList(companyId, CameraConfigType.ABCKAM.getNumber(), newestCameras);
    }

    private void saveCameraList(Long companyId, int type, List<CameraSourceDto> newestCameraList) {
        var example = new CameraExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andTypeEqualTo((byte) type);
        List<Camera> existCameraList = cameraMapper.selectByExample(example);
        syncCameraLists(companyId, type, existCameraList, newestCameraList);
    }

    public void syncCameraLists(
            Long companyId, int type, List<Camera> existCameraList, List<CameraSourceDto> newestCameraList) {
        // 转换为 Map 方便查找
        var existCameraMap =
                existCameraList.stream().collect(Collectors.toMap(Camera::getOriginCameraId, camera -> camera));
        var newestCameraMap = newestCameraList.stream()
                .collect(Collectors.toMap(CameraSourceDto::getOriginCameraId, camera -> camera));

        // 1. 插入不存在于 existCameraList 中的数据
        for (var newestCamera : newestCameraList) {
            if (!existCameraMap.containsKey(newestCamera.getOriginCameraId())) {
                initOneCamera(companyId, type, newestCamera);
            }
        }
        // 2. 更新已存在的数据
        for (Camera existCamera : existCameraList) {
            var newestCamera = newestCameraMap.get(existCamera.getOriginCameraId());
            if (newestCamera != null) {
                updateCamera(
                        existCamera.getId(),
                        OriginStatus.VALID_VALUE,
                        newestCamera.getOriginCameraTitle(),
                        newestCamera.getVideoUrl());
            } else {
                updateCamera(existCamera.getId(), OriginStatus.NOT_FOUND_VALUE, null, null);
            }
        }
    }

    private void updateCamera(Long id, Integer originStatus, String originCameraTitle, String videoUrl) {
        cameraMapper.updateByPrimaryKeySelective(Camera.builder()
                .id(id)
                .originCameraTitle(originCameraTitle)
                .originStatus(originStatus)
                .videoUrl(videoUrl)
                .updatedAt(LocalDateTime.now())
                .build());
    }

    private void initOneCamera(Long companyId, int type, CameraSourceDto sourceDto) {
        var camera = cameraConvert.toCamera(sourceDto);
        camera.setCompanyId(companyId);
        camera.setOriginStatus(OriginStatus.VALID_VALUE);
        camera.setType((byte) type);
        camera.setIsActive(false);
        // video url 为空时，生成并记录 html
        camera.setVideoUrl(sourceDto.getVideoUrl());
        if (camera.getType() == CameraConfigType.IDOGCAM.getNumber() && !StringUtils.hasText(sourceDto.getVideoUrl())) {
            camera.setVideoUrl(cameraIdogcamService.generateAndRecordHtml(camera));
        }
        camera.setAuth(sourceDto.getAuth());
        camera.setVisibilityType(VisibilityType.PUBLIC.getNumber());
        camera.setCreatedAt(LocalDateTime.now());
        camera.setUpdatedAt(LocalDateTime.now());
        cameraMapper.insertSelective(camera);
    }

    public void updateCamera(Long companyId, Camera camera) {
        camera.setCompanyId(companyId);
        camera.setUpdatedAt(LocalDateTime.now());
        cameraMapper.updateByPrimaryKeySelective(camera);
    }
}
