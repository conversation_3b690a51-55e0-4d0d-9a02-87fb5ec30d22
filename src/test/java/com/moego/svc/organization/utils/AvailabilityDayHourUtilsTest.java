package com.moego.svc.organization.utils;

import com.google.type.DayOfWeek;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

class AvailabilityDayHourUtilsTest {

    List<SlotAvailabilityDay> get28Days() {
        List<SlotAvailabilityDay> slotDays = new ArrayList<>();
        Arrays.stream(ScheduleType.values()).forEach(scheduleType -> {
            if (scheduleType == ScheduleType.UNRECOGNIZED || scheduleType == ScheduleType.SCHEDULE_TYPE_UNSPECIFIED) {
                return;
            }
            Arrays.stream(DayOfWeek.values()).forEach(dayOfWeek -> {
                if (dayOfWeek == DayOfWeek.UNRECOGNIZED || dayOfWeek == DayOfWeek.DAY_OF_WEEK_UNSPECIFIED) {
                    return;
                }
                slotDays.add(SlotAvailabilityDay.newBuilder()
                        .setDayOfWeek(dayOfWeek)
                        .setScheduleType(scheduleType)
                        .build());
            });
        });
        return slotDays;
    }

    @Test
    void getSlotDayByStartEndTime() {
        LocalDate slotStartSunday = LocalDate.parse("2025-05-18");
        String inputStartDate = "2025-05-18"; // 第一周
        ScheduleType scheduleType = ScheduleType.forNumber(1); // 1周循环

        var days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        // 检查days
        assert days.size() == 7;
        assert days.get("2025-05-18").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-05-18").getScheduleType() == ScheduleType.ONE_WEEK;
        assert days.get("2025-05-24").getDayOfWeek() == DayOfWeek.SATURDAY;
        assert days.get("2025-05-24").getScheduleType() == ScheduleType.ONE_WEEK;

        // 1周循环
        inputStartDate = "2025-05-25"; // 第2个轮回，应该返回第一周的数据
        days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        assert days.size() == 7;
        assert days.get("2025-05-25").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-05-25").getScheduleType() == ScheduleType.ONE_WEEK;
        assert days.get("2025-05-31").getDayOfWeek() == DayOfWeek.SATURDAY;
        assert days.get("2025-05-31").getScheduleType() == ScheduleType.ONE_WEEK;

        // 2周循环
        scheduleType = ScheduleType.forNumber(2);
        inputStartDate = "2025-05-25"; // 第2周
        days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        assert days.size() == 7;
        assert days.get("2025-05-25").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-05-25").getScheduleType() == ScheduleType.TWO_WEEK;
        assert days.get("2025-05-31").getDayOfWeek() == DayOfWeek.SATURDAY;
        assert days.get("2025-05-31").getScheduleType() == ScheduleType.TWO_WEEK;

        // 2周循环 下一个轮回的第2周
        scheduleType = ScheduleType.forNumber(2);
        inputStartDate = "2025-06-08";
        days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        assert days.get("2025-06-08").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-06-08").getScheduleType() == ScheduleType.TWO_WEEK;
        assert days.get("2025-06-14").getDayOfWeek() == DayOfWeek.SATURDAY;
        assert days.get("2025-06-14").getScheduleType() == ScheduleType.TWO_WEEK;

        // 3 周循环
        scheduleType = ScheduleType.forNumber(3);
        inputStartDate = "2025-06-01"; // 应该返回third_week
        days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        assert days.get("2025-06-01").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-06-01").getScheduleType() == ScheduleType.THREE_WEEK;

        inputStartDate = "2025-06-08"; // 应该返回first_week
        days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        assert days.get("2025-06-08").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-06-08").getScheduleType() == ScheduleType.ONE_WEEK;

        // 4 周循环
        scheduleType = ScheduleType.forNumber(4);
        // 第一个轮回的第2周
        inputStartDate = "2025-05-25";
        days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        assert days.get("2025-05-25").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-05-25").getScheduleType() == ScheduleType.TWO_WEEK;

        inputStartDate = "2025-06-15"; // 第1周
        days = AvailabilityDayHourUtils.getSlotDayByStartEndTime(
                slotStartSunday, inputStartDate, scheduleType, get28Days());
        assert days.get("2025-06-15").getDayOfWeek() == DayOfWeek.SUNDAY;
        assert days.get("2025-06-15").getScheduleType() == ScheduleType.ONE_WEEK;
        assert days.get("2025-06-21").getDayOfWeek() == DayOfWeek.SATURDAY;
        assert days.get("2025-06-21").getScheduleType() == ScheduleType.ONE_WEEK;
    }
}
