package com.moego.svc.organization.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.svc.organization.entity.MoeBusiness;
import com.moego.svc.organization.mapper.MoeBusinessMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BusinessServiceTest {
    @Mock
    private MoeBusinessMapper moeBusinessMapper;

    @InjectMocks
    private BusinessService businessService;

    @Test
    void testGetCompanyId_CacheEffectiveness() {
        Long businessId = 1L;
        Long companyId = 100L;
        MoeBusiness moeBusiness = new MoeBusiness();
        moeBusiness.setCompanyId(companyId.intValue());

        when(moeBusinessMapper.useDataSource(any())).thenReturn(moeBusinessMapper);
        when(moeBusinessMapper.selectByPrimaryKey(businessId.intValue())).thenReturn(moeBusiness);

        // 第一次调用，触发数据库
        Long result1 = businessService.getCompanyId(businessId);
        assertThat(result1).isEqualTo(companyId);

        // 第二次调用，应该命中缓存，不再触发数据库
        Long result2 = businessService.getCompanyId(businessId);
        assertThat(result2).isEqualTo(companyId);

        // 验证 selectByPrimaryKey 只被调用一次
        verify(moeBusinessMapper, times(1)).selectByPrimaryKey(businessId.intValue());
    }
}
