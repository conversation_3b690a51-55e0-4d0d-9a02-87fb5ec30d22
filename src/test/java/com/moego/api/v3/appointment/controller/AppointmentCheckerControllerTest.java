package com.moego.api.v3.appointment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.never;

import com.google.type.Date;
import com.moego.api.v3.appointment.service.AppointmentCheckerService;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.idl.api.appointment.v1.AppointmentDateConflictCheckResult;
import com.moego.idl.api.appointment.v1.BusinessClosedDateCheckResult;
import com.moego.idl.api.appointment.v1.LodgingOverCapacityCheckResult;
import com.moego.idl.api.appointment.v1.LodgingUnitChange;
import com.moego.idl.api.appointment.v1.LodgingUnitOverview;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.organization.v1.CloseDateDef;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListRequest;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AppointmentCheckerControllerTest {

    @Mock
    private CompanySettingService companySettingService;

    @Mock
    private AppointmentCheckerService appointmentCheckerService;

    @Mock
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentService;

    @Mock
    private com.moego.idl.service.online_booking.v1.OBAvailableDateTimeServiceGrpc
                    .OBAvailableDateTimeServiceBlockingStub
            obAvailableDateTimeServiceBlockingStub;

    @Mock
    private com.moego.api.v3.appointment.service.PetDetailService petDetailService;

    @InjectMocks
    private AppointmentCheckerController controller;

    private final Long companyId = 1L;
    private final Long businessId = 100L;
    private final Date startDate = createDate(2023, 10, 1);
    private final Date endDate = createDate(2023, 10, 5);

    private static Date createDate(int year, int month, int day) {
        return Date.newBuilder().setYear(year).setMonth(month).setDay(day).build();
    }

    @Nested
    @DisplayName("buildLodgingOverCapacityCheckResult Tests")
    class BuildLodgingOverCapacityCheckResultTests {

        private final Long serviceId = 200L;
        private final Long evaluationServiceId = 201L;
        private final List<Long> lodgingUnitIds = List.of(301L, 302L);

        @Test
        @DisplayName("Should return empty result when no lodging units found")
        void shouldReturnEmptyResultWhenNoLodgingUnitsFound() {
            // Arrange
            when(appointmentCheckerService.getLodgingUnitList(companyId, businessId, lodgingUnitIds))
                    .thenReturn(new ArrayList<>());

            // Act
            LodgingOverCapacityCheckResult result = controller.buildLodgingOverCapacityCheckResult(
                    companyId, businessId, startDate, endDate, lodgingUnitIds, 0L, null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getLodgingUnitsList()).isEmpty();
            assertThat(result.getLodgingTypesList()).isEmpty();
        }

        @Test
        @DisplayName("Should return over capacity lodging units")
        void shouldReturnOverCapacityLodgingUnits() {
            // Arrange
            List<LodgingUnitModel> lodgingUnitList = createLodgingUnitModels();
            List<LodgingTypeModel> lodgingTypeList = createLodgingTypeModels();
            Map<Long, LodgingOccupiedStatus> lodgingStatusMap = createLodgingStatusMap();

            when(appointmentCheckerService.getLodgingUnitList(companyId, businessId, lodgingUnitIds))
                    .thenReturn(lodgingUnitList);
            when(appointmentCheckerService.getLodgingTypeList(lodgingUnitList)).thenReturn(lodgingTypeList);
            String startDateStr = "2023-10-01";
            String endDateStr = "2023-10-05";
            when(appointmentCheckerService.getLodgingOccupiedStatusMap(
                            companyId, businessId, startDateStr, endDateStr, lodgingTypeList, lodgingUnitList))
                    .thenReturn(lodgingStatusMap);

            // Mock filterOverCapacityUnits to return the units that should be over capacity
            List<LodgingUnitModel> overCapacityUnits = new ArrayList<>(List.of(
                lodgingUnitList.get(0), // Unit 1
                lodgingUnitList.get(1)  // Unit 2
            ));
            when(appointmentCheckerService.filterOverCapacityUnits(lodgingUnitList, lodgingStatusMap, any()))
                    .thenReturn(overCapacityUnits);

            // Act
            LodgingOverCapacityCheckResult result = controller.buildLodgingOverCapacityCheckResult(
                    companyId, businessId, startDate, endDate, lodgingUnitIds, 0L, null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getLodgingTypesList()).hasSize(2);
            assertThat(result.getLodgingUnitsList()).hasSize(2);

            // Verify lodging types
            assertThat(result.getLodgingTypesList().get(0).getId()).isEqualTo(1001L);
            assertThat(result.getLodgingTypesList().get(0).getName()).isEqualTo("Type 1");
            assertThat(result.getLodgingTypesList().get(1).getId()).isEqualTo(1002L);
            assertThat(result.getLodgingTypesList().get(1).getName()).isEqualTo("Type 2");

            // Verify lodging units (only fully occupied or partially occupied ROOM type should be included)
            assertThat(result.getLodgingUnitsList().get(0).getId()).isEqualTo(301L);
            assertThat(result.getLodgingUnitsList().get(0).getName()).isEqualTo("Unit 1");
            assertThat(result.getLodgingUnitsList().get(1).getId()).isEqualTo(303L);
            assertThat(result.getLodgingUnitsList().get(1).getName()).isEqualTo("Unit 3");
        }

        private List<LodgingUnitModel> createLodgingUnitModels() {
            return new ArrayList<>(List.of(
                    LodgingUnitModel.newBuilder()
                            .setId(301L)
                            .setName("Unit 1")
                            .setLodgingTypeId(1001L)
                            .build(),
                    LodgingUnitModel.newBuilder()
                            .setId(302L)
                            .setName("Unit 2")
                            .setLodgingTypeId(1001L)
                            .build(),
                    LodgingUnitModel.newBuilder()
                            .setId(303L)
                            .setName("Unit 3")
                            .setLodgingTypeId(1002L)
                            .build(),
                    LodgingUnitModel.newBuilder()
                            .setId(304L)
                            .setName("Unit 4")
                            .setLodgingTypeId(1002L)
                            .build()));
        }

        private List<LodgingTypeModel> createLodgingTypeModels() {
            return List.of(
                    LodgingTypeModel.newBuilder()
                            .setId(1001L)
                            .setName("Type 1")
                            .setLodgingUnitType(LodgingUnitType.AREA)
                            .build(),
                    LodgingTypeModel.newBuilder()
                            .setId(1002L)
                            .setName("Type 2")
                            .setLodgingUnitType(LodgingUnitType.ROOM)
                            .build());
        }

        private Map<Long, LodgingOccupiedStatus> createLodgingStatusMap() {
            Map<Long, LodgingOccupiedStatus> statusMap = new HashMap<>();
            // Unit 1 is fully occupied (should be included)
            statusMap.put(301L, LodgingOccupiedStatus.FULLY_OCCUPIED);
            // Unit 2 is available (should not be included)
            statusMap.put(302L, LodgingOccupiedStatus.VACANT);
            // Unit 3 is partially occupied and ROOM type (should be included)
            statusMap.put(303L, LodgingOccupiedStatus.PARTIALLY_OCCUPIED);
            // Unit 4 is partially occupied but not ROOM type (should not be included)
            statusMap.put(304L, LodgingOccupiedStatus.VACANT);
            return statusMap;
        }

        @Test
        @DisplayName("Should handle appointment ID with changed lodging unit")
        void shouldHandleAppointmentIdWithChangedLodgingUnit() {
            // Arrange
            Long appointmentId = 1001L;
            Long oldLodgingUnitId = 301L;
            Long newLodgingUnitId = 305L;

            LodgingUnitChange changedLodgingUnit = LodgingUnitChange.newBuilder()
                    .setOldLodgingUnitId(oldLodgingUnitId)
                    .setNewLodgingUnitId(newLodgingUnitId)
                    .build();

            // Mock original lodging units
            List<LodgingUnitModel> originalLodgingUnitList = createLodgingUnitModels();
            List<LodgingTypeModel> lodgingTypeList = createLodgingTypeModels();

            // Mock appointment
            AppointmentModel appointment = AppointmentModel.newBuilder()
                    .setId(appointmentId)
                    .setAppointmentDate("2023-10-02")
                    .setAppointmentEndDate("2023-10-04")
                    .build();

            // Mock pet detail response
            GetPetDetailListResponse petDetailResp = GetPetDetailListResponse.newBuilder()
                    .addPetDetails(PetDetailModel.newBuilder()
                            .setGroomingId(appointmentId)
                            .setLodgingId(newLodgingUnitId)
                            .build())
                    .build();

            // Mock updated lodging units after appointment processing
            List<LodgingUnitModel> updatedLodgingUnits = new ArrayList<>(List.of(
                    LodgingUnitModel.newBuilder()
                            .setId(newLodgingUnitId)
                            .setName("New Unit 5")
                            .setLodgingTypeId(1002L)
                            .build()));

            // Mock over capacity units
            List<LodgingUnitModel> overCapacityUnits = new ArrayList<>(List.of(updatedLodgingUnits.get(0)));

            when(appointmentService.getAppointment(any(GetAppointmentRequest.class)))
                    .thenReturn(GetAppointmentResponse.newBuilder()
                            .setAppointment(appointment)
                            .build());
            when(appointmentCheckerService.getLodgingUnitList(companyId, businessId, lodgingUnitIds))
                    .thenReturn(originalLodgingUnitList);
            when(appointmentCheckerService.getLodgingTypeList(originalLodgingUnitList))
                    .thenReturn(lodgingTypeList);
            when(petDetailService.getPetDetailsWithActualDates(companyId, List.of(appointmentId)))
                    .thenReturn(petDetailResp);
            when(appointmentCheckerService.getLodgingUnitList(companyId, businessId, List.of(newLodgingUnitId)))
                    .thenReturn(updatedLodgingUnits);

            // Mock date range calculation
            AppointmentCheckerService.DateRange dateRange =
                    new AppointmentCheckerService.DateRange(LocalDate.of(2023, 10, 1), LocalDate.of(2023, 10, 1));
            when(appointmentCheckerService.getNonOverlapRanges(any(), any(), any(), any()))
                    .thenReturn(dateRange);

            // Mock lodging status and filter
            Map<Long, LodgingOccupiedStatus> lodgingStatusMap = Map.of(
                    newLodgingUnitId, LodgingOccupiedStatus.FULLY_OCCUPIED);
            when(appointmentCheckerService.getLodgingOccupiedStatusMap(
                            eq(companyId), eq(businessId), eq("2023-10-01"), eq("2023-10-01"),
                            eq(lodgingTypeList), any()))
                    .thenReturn(lodgingStatusMap);
            when(appointmentCheckerService.filterOverCapacityUnits(any(), any(), any()))
                    .thenReturn(overCapacityUnits);

            // Act
            LodgingOverCapacityCheckResult result = controller.buildLodgingOverCapacityCheckResult(
                    companyId, businessId, startDate, endDate, lodgingUnitIds, appointmentId, changedLodgingUnit);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getLodgingUnitsList()).hasSize(1);
            assertThat(result.getLodgingUnitsList().get(0).getId()).isEqualTo(newLodgingUnitId);

            // Verify appointment service was called
            verify(appointmentService).getAppointment(any(GetAppointmentRequest.class));
            verify(petDetailService).getPetDetailsWithActualDates(companyId, List.of(appointmentId));
        }

        @Test
        @DisplayName("Should handle appointment ID without changed lodging unit")
        void shouldHandleAppointmentIdWithoutChangedLodgingUnit() {
            // Arrange
            Long appointmentId = 1001L;

            List<LodgingUnitModel> originalLodgingUnitList = createLodgingUnitModels();
            List<LodgingTypeModel> lodgingTypeList = createLodgingTypeModels();
            Map<Long, LodgingOccupiedStatus> lodgingStatusMap = createLodgingStatusMap();

            // Mock appointment
            AppointmentModel appointment = AppointmentModel.newBuilder()
                    .setId(appointmentId)
                    .setAppointmentDate("2023-10-02")
                    .setAppointmentEndDate("2023-10-04")
                    .build();

            // Mock pet detail response
            GetPetDetailListResponse petDetailResp = GetPetDetailListResponse.newBuilder()
                    .addPetDetails(PetDetailModel.newBuilder()
                            .setGroomingId(appointmentId)
                            .setLodgingId(301L)
                            .build())
                    .build();

            List<LodgingUnitModel> updatedLodgingUnits = new ArrayList<>(List.of(originalLodgingUnitList.get(0)));
            List<LodgingUnitModel> overCapacityUnits = new ArrayList<>(List.of(originalLodgingUnitList.get(0)));

            when(appointmentService.getAppointment(any(GetAppointmentRequest.class)))
                    .thenReturn(GetAppointmentResponse.newBuilder().setAppointment(appointment).build());
            when(appointmentCheckerService.getLodgingUnitList(companyId, businessId, lodgingUnitIds))
                    .thenReturn(originalLodgingUnitList);
            when(appointmentCheckerService.getLodgingTypeList(originalLodgingUnitList))
                    .thenReturn(lodgingTypeList);
            when(petDetailService.getPetDetailsWithActualDates(companyId, List.of(appointmentId)))
                    .thenReturn(petDetailResp);
            when(appointmentCheckerService.getLodgingUnitList(companyId, businessId, List.of(301L)))
                    .thenReturn(updatedLodgingUnits);
            when(appointmentCheckerService.getLodgingOccupiedStatusMap(
                            eq(companyId), eq(businessId), eq("2023-10-01"), eq("2023-10-05"),
                            eq(lodgingTypeList), eq(updatedLodgingUnits)))
                    .thenReturn(lodgingStatusMap);
            when(appointmentCheckerService.filterOverCapacityUnits(any(), any(), any()))
                    .thenReturn(overCapacityUnits);

            // Act
            LodgingOverCapacityCheckResult result = controller.buildLodgingOverCapacityCheckResult(
                    companyId, businessId, startDate, endDate, lodgingUnitIds, appointmentId, null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getLodgingTypesList()).hasSize(2);
            assertThat(result.getLodgingUnitsList()).hasSize(1);

            // Verify appointment service was called
            verify(appointmentService).getAppointment(any(GetAppointmentRequest.class));
            verify(petDetailService).getPetDetailsWithActualDates(companyId, List.of(appointmentId));
        }

        @Test
        @DisplayName("Should not call appointment service when appointment ID is 0")
        void shouldNotCallAppointmentServiceWhenAppointmentIdIsZero() {
            // Arrange
            List<LodgingUnitModel> lodgingUnitList = createLodgingUnitModels();
            List<LodgingTypeModel> lodgingTypeList = createLodgingTypeModels();
            Map<Long, LodgingOccupiedStatus> lodgingStatusMap = createLodgingStatusMap();

            when(appointmentCheckerService.getLodgingUnitList(companyId, businessId, lodgingUnitIds))
                    .thenReturn(lodgingUnitList);
            when(appointmentCheckerService.getLodgingTypeList(lodgingUnitList)).thenReturn(lodgingTypeList);
            String startDateStr = "2023-10-01";
            String endDateStr = "2023-10-05";
            when(appointmentCheckerService.getLodgingOccupiedStatusMap(
                            companyId, businessId, startDateStr, endDateStr, lodgingTypeList, lodgingUnitList))
                    .thenReturn(lodgingStatusMap);

            List<LodgingUnitModel> overCapacityUnits = new ArrayList<>(List.of(lodgingUnitList.get(0)));
            when(appointmentCheckerService.filterOverCapacityUnits(any(), any(), any()))
                    .thenReturn(overCapacityUnits);

            // Act
            LodgingOverCapacityCheckResult result = controller.buildLodgingOverCapacityCheckResult(
                    companyId, businessId, startDate, endDate, lodgingUnitIds, 0L, null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getLodgingTypesList()).hasSize(2);
            assertThat(result.getLodgingUnitsList()).hasSize(1);

            // Verify appointment service was NOT called
            verify(appointmentService, never()).getAppointment(any(GetAppointmentRequest.class));
            verify(petDetailService, never()).getPetDetailsWithActualDates(any(), any());
        }


    }

    @Nested
    @DisplayName("buildAppointmentDateConflictCheckResult Tests")
    class BuildAppointmentDateConflictCheckResultTests {

        private final List<Long> petIds = List.of(401L, 402L);
        private final Long customerId = 500L;
        private final Long appointmentId = 600L;
        private final String timezone = "America/Los_Angeles";

        @Test
        @DisplayName("Should return empty result when no appointment conflicts found")
        void shouldReturnEmptyResultWhenNoAppointmentConflictsFound() {
            // Arrange
            when(companySettingService.mustGetTimeZoneName(companyId)).thenReturn(timezone);
            when(appointmentCheckerService.getTimeOverlapAppointmentListMap(
                            eq(companyId),
                            anyList(),
                            any(GetTimeOverlapAppointmentListRequest.Filter.class),
                            eq(petIds),
                            eq(appointmentId)))
                    .thenReturn(new HashMap<>());

            // Act
            AppointmentDateConflictCheckResult result = controller.buildAppointmentDateConflictCheckResult(
                    companyId, startDate, endDate, petIds, customerId, appointmentId);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getConflictAppointmentsList()).isEmpty();
        }

        @Test
        @DisplayName("Should return appointment conflicts when found")
        void shouldReturnAppointmentConflictsWhenFound() {
            // Arrange
            when(companySettingService.mustGetTimeZoneName(companyId)).thenReturn(timezone);

            // Mock appointment list map
            Map<Long, List<AppointmentModel>> appointmentListMap = createAppointmentListMap();
            when(appointmentCheckerService.getTimeOverlapAppointmentListMap(
                            eq(companyId),
                            anyList(),
                            any(GetTimeOverlapAppointmentListRequest.Filter.class),
                            eq(petIds),
                            eq(appointmentId)))
                    .thenReturn(appointmentListMap);

            // Mock pet detail list
            GetPetDetailListResponse petDetailListResponse = createPetDetailListResponse();
            when(appointmentCheckerService.getPetDetailList(eq(companyId), anyList()))
                    .thenReturn(petDetailListResponse);

            // Mock pet map
            Map<Long, BusinessCustomerPetModel> petMap = createPetMap();
            when(appointmentCheckerService.getPetMap(eq(companyId), any(GetPetDetailListResponse.class)))
                    .thenReturn(petMap);

            // Mock service map
            Map<Long, ServiceBriefView> serviceMap = createServiceMap();
            when(appointmentCheckerService.getServiceMap(eq(companyId), any(GetPetDetailListResponse.class)))
                    .thenReturn(serviceMap);

            // Act
            AppointmentDateConflictCheckResult result = controller.buildAppointmentDateConflictCheckResult(
                    companyId, startDate, endDate, petIds, customerId, appointmentId);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getConflictAppointmentsList()).isNotEmpty();
            // Only pet 401 has appointments
            assertThat(result.getConflictAppointmentsList()).hasSize(1);
            assertThat(result.getConflictAppointmentsList().get(0).getPet().getId())
                    .isEqualTo(401L);
            assertThat(result.getConflictAppointmentsList().get(0).getAppointmentsList())
                    .hasSize(1);
        }

        private Map<Long, List<AppointmentModel>> createAppointmentListMap() {
            Map<Long, List<AppointmentModel>> map = new HashMap<>();
            // Pet 401 has one appointment
            map.put(
                    401L,
                    List.of(AppointmentModel.newBuilder()
                            .setId(601L)
                            .setAppointmentDate("2023-10-02")
                            .setAppointmentEndDate("2023-10-03")
                            .build()));
            // Pet 402 has no appointments
            map.put(402L, new ArrayList<>());
            return map;
        }

        private GetPetDetailListResponse createPetDetailListResponse() {
            return GetPetDetailListResponse.newBuilder()
                    .addPetDetails(PetDetailModel.newBuilder()
                            .setId(701L)
                            .setGroomingId(601L)
                            .setPetId(401L)
                            .setServiceId(801L)
                            .setStartDate("2023-10-02")
                            .setEndDate("2023-10-03")
                            .build())
                    .build();
        }

        private Map<Long, BusinessCustomerPetModel> createPetMap() {
            Map<Long, BusinessCustomerPetModel> map = new HashMap<>();
            map.put(
                    401L,
                    BusinessCustomerPetModel.newBuilder()
                            .setId(401L)
                            .setPetName("Pet 1")
                            .build());
            map.put(
                    402L,
                    BusinessCustomerPetModel.newBuilder()
                            .setId(402L)
                            .setPetName("Pet 2")
                            .build());
            return map;
        }

        private Map<Long, ServiceBriefView> createServiceMap() {
            Map<Long, ServiceBriefView> map = new HashMap<>();
            map.put(
                    801L,
                    ServiceBriefView.newBuilder()
                            .setId(801L)
                            .setName("Service 1")
                            .build());
            return map;
        }
    }

    @Nested
    @DisplayName("buildBusinessClosedDateCheckResult Tests")
    class BuildBusinessClosedDateCheckResultTests {

        @Test
        @DisplayName("Should return empty result when no closed dates found")
        void shouldReturnEmptyResultWhenNoClosedDatesFound() {
            // Arrange
            when(appointmentCheckerService.getClosedDateList(companyId, businessId))
                    .thenReturn(new ArrayList<>());

            // Act
            BusinessClosedDateCheckResult result =
                    controller.buildBusinessClosedDateCheckResult(companyId, businessId, startDate, endDate);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getClosedDateList()).isEmpty();
        }

        @Test
        @DisplayName("Should return closed dates that overlap with appointment date range")
        void shouldReturnClosedDatesThatOverlapWithAppointmentDateRange() {
            // Arrange
            List<CloseDateDef> closedDateDefList = createClosedDateDefList();
            when(appointmentCheckerService.getClosedDateList(companyId, businessId))
                    .thenReturn(closedDateDefList);

            // Act
            BusinessClosedDateCheckResult result =
                    controller.buildBusinessClosedDateCheckResult(companyId, businessId, startDate, endDate);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getClosedDateList()).hasSize(4);
            assertThat(result.getClosedDateList()).contains("2023-10-01", "2023-10-02", "2023-10-03", "2023-10-04");
            // 2023-09-30 and 2023-10-06 are outside the appointment date range
            assertThat(result.getClosedDateList()).doesNotContain("2023-09-30", "2023-10-06");
        }

        private List<CloseDateDef> createClosedDateDefList() {
            return List.of(
                    // Closed date range that partially overlaps with appointment date range
                    CloseDateDef.newBuilder()
                            .setStartDate("2023-09-30")
                            .setEndDate("2023-10-03")
                            .build(),
                    // Closed date range that is completely within appointment date range
                    CloseDateDef.newBuilder()
                            .setStartDate("2023-10-04")
                            .setEndDate("2023-10-04")
                            .build(),
                    // Closed date range that is completely outside appointment date range
                    CloseDateDef.newBuilder()
                            .setStartDate("2023-10-06")
                            .setEndDate("2023-10-07")
                            .build());
        }
    }
}
