package com.moego.common.enums;

public interface OnlineBookingConst {
    byte AVAILABLE_TIME_TYPE_WORKING_HOUR = 0;
    byte AVAILABLE_TIME_TYPE_BY_SLOT = 1;
    byte AVAILABLE_TIME_TYPE_DISABLE = 2;
    byte SYNC_WITH_WORKING_HOUR_DISABLE = 0;
    byte SYNC_WITH_WORKING_HOUR_ENABLE = 1;

    byte STAFF_AVAILABLE_DISABLE = 0;
    byte STAFF_AVAILABLE_ENABLE = 1;

    // only show applicable service开关
    byte SERVICE_FILTER_DISABLE = 0;
    byte SERVICE_FILTER_ENABLE = 1;

    byte VERSION_TWO = 2;

    // no show protection类型
    Byte NO_SHOW_PROTECTION_DISABLE = 0;
    Byte NO_SHOW_PROTECTION_CARD_ON_FILE = 1;
    Byte NO_SHOW_PROTECTION_PREPAY = 2;
    Byte NO_SHOW_PROTECTION_PREAUTH = 3;

    // prepay类型
    Byte PREPAY_TYPE_FULL_PAY = 0;
    Byte PREPAY_TYPE_DEPOSIT = 1;

    String PREPAY_GUID_PREFIX = "ob:prepay:";
    String ONLINE_BOOKING_PAYMENT_SETTING = "ob:payment:client:setting:%s:%s";

    // deposit类型
    Byte DEPOSIT_TYPE_BY_FIXED_AMOUNT = 0;
    Byte DEPOSIT_TYPE_BY_PERCENTAGE = 1;

    // prepay tip开关
    Byte PREPAY_TIP_DISABLE = 0;
    Byte PREPAY_TIP_ENABLE = 1;

    /**
     * 时间间隔类型 Exact times
     */
    Byte TIME_SLOT_FORMAT_EXACT_TIMES = 1;

    /**
     * 时间间隔类型 Arrival window
     */
    Byte TIME_SLOT_FORMAT_ARRIVAL_WINDOW = 2;
}
