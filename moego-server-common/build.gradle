plugins {
    id 'io.spring.dependency-management'
}

dependencies {
    compileOnly 'org.springframework.boot:spring-boot-starter-web'
    compileOnly 'org.springframework.boot:spring-boot-starter-validation'
    compileOnly 'org.springframework.boot:spring-boot-starter-data-redis'
    compileOnly 'com.github.pagehelper:pagehelper-spring-boot-starter:1.4.7'
    // TODO(Freeman): should be removed
    api 'io.sentry:sentry:6.4.1'
    api 'com.google.code.gson:gson'
    api "com.auth0:java-jwt:3.4.0"
    api("io.swagger.core.v3:swagger-annotations-jakarta:2.2.15")
    api 'com.googlecode.libphonenumber:libphonenumber:8.12.16'
    compileOnly("com.github.spotbugs:spotbugs-annotations:${spotbugsAnnotationsVersion}")
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation 'org.springframework.boot:spring-boot-starter-validation'
    testImplementation 'org.springframework.boot:spring-boot-starter-data-redis'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
}
