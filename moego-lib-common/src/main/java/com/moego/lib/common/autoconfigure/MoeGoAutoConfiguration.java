package com.moego.lib.common.autoconfigure;

import com.moego.lib.common.autoconfigure.feature.Auth;
import com.moego.lib.common.autoconfigure.feature.DataSource;
import com.moego.lib.common.autoconfigure.feature.DistributedTracing;
import com.moego.lib.common.autoconfigure.feature.ExceptionHandler;
import com.moego.lib.common.autoconfigure.feature.ExceptionTracker;
import com.moego.lib.common.autoconfigure.feature.FeignClient;
import com.moego.lib.common.autoconfigure.feature.Health;
import com.moego.lib.common.autoconfigure.feature.Jackson;
import com.moego.lib.common.autoconfigure.feature.Logging;
import com.moego.lib.common.autoconfigure.feature.Metadata;
import com.moego.lib.common.autoconfigure.feature.Metrics;
import com.moego.lib.common.autoconfigure.feature.RPC;
import com.moego.lib.common.autoconfigure.feature.Redis;
import com.moego.lib.common.autoconfigure.feature.Validation;
import com.moego.lib.common.autoconfigure.grpc.GrpcConfiguration;
import com.moego.lib.common.autoconfigure.http.HttpConfiguration;
import com.moego.lib.common.observability.metrics.prometheus.database.HikariCP;
import com.moego.lib.common.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @since 2022/9/29
 */
@Configuration(proxyBeanMethods = false)
@Import({GrpcConfiguration.class, HttpConfiguration.class})
public class MoeGoAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(MoeGoAutoConfiguration.class);

    public MoeGoAutoConfiguration(ApplicationContext applicationContext) {
        log.info("Powered by MoeGo ^_^");

        SpringUtil.setContext(applicationContext);
    }

    @Configuration(proxyBeanMethods = false)
    @Import({
        Auth.class,
        DistributedTracing.class,
        Logging.class,
        Metadata.class,
        Metrics.class,
        Validation.class,
        Health.class,
        ExceptionHandler.class,
        ExceptionTracker.class,
        FeignClient.class,
        RPC.class,
        Jackson.class,
        HikariCP.class,
        DataSource.class,
        Redis.class,
    })
    static class FeatureConfiguration {}
}
