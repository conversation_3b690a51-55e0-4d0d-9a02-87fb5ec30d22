package com.moego.lib.common.autoconfigure.http;

import static org.assertj.core.api.Assertions.assertThat;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.freemanan.cr.core.anno.Verb;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link HttpClientConfiguration} tester.
 */
@ClasspathReplacer({@Action(verb = Verb.EXCLUDE, value = "spring-cloud-openfeign-core-*.jar")})
class HttpClientConfigurationNoFeignTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(HttpClientConfiguration.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(HttpClientConfiguration.class);
            assertThat(context).doesNotHaveBean("moeFeignDecoder");
        });
    }
}
